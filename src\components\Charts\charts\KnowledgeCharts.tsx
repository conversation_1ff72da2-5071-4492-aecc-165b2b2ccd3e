import { useState, useMemo } from "react";
import MetricCard from "@/components/Charts/MetricCard";
import MetricDetails from "@/components/Charts/MetricDetails";
import { LocationFilterState } from "@/types/locationFilter";

interface KnowledgeChartsProps {
  locationFilters?: LocationFilterState;
}

const KnowledgeCharts = ({ locationFilters }: KnowledgeChartsProps) => {
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});


  // Knowledge metrics data
  const overviewMetrics = [
    {
      id: "knowledge-index",
      title: "Knowledge Index",
      value: "85",
      unit: "Index Score",
      target: 100,
      targetPercentage: 15,
      trend: [78, 79, 80, 81, 82, 83, 84, 83, 82, 83, 84, 85],
      isImproving: true
    },
    {
      id: "reactivation-index",
      title: "Reactivation Index",
      value: "72",
      unit: "Index Score",
      target: 100,
      targetPercentage: 28,
      trend: [65, 66, 67, 68, 69, 70, 71, 70, 69, 70, 71, 72],
      isImproving: true
    },
    {
      id: "knowledge-repository-access",
      title: "% of users who have accessed the knowledge repository",
      value: "68%",
      unit: "Users",
      target: 100,
      targetPercentage: 32,
      trend: [60, 61, 62, 63, 64, 65, 66, 65, 64, 65, 67, 68],
      isImproving: true
    },
    {
      id: "learning-tasks-completion",
      title: "% completion rate of assigned learning tasks",
      value: "91%",
      unit: "Tasks",
      target: 100,
      targetPercentage: 9,
      trend: [85, 86, 87, 88, 89, 90, 91, 90, 89, 90, 91, 91],
      isImproving: true
    }
  ];

  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  return (
    <div className="space-y-6">

      {/* Headline Metrics */}
      <div className="grid grid-cols-4 gap-4">
        {overviewMetrics.map((metric) => (
          <MetricCard
            key={metric.id}
            {...metric}
            showDetails={openDetailsId === metric.id}
            onToggleDetails={() => handleToggleDetails(metric.id)}
            selectedPeriod={getSelectedPeriod(metric.id)}
            onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
          />
        ))}
      </div>

      {/* Details Section - Rendered below the grid when a card is selected */}
      {openDetailsId && (() => {
        const selectedMetric = overviewMetrics.find(m => m.id === openDetailsId);
        return selectedMetric ? (
          <MetricDetails
            title={selectedMetric.title}
            value={selectedMetric.value}
            unit={selectedMetric.unit}
            targetPercentage={selectedMetric.targetPercentage}
            trend={selectedMetric.trend}
            isImproving={selectedMetric.isImproving}
            selectedPeriod={getSelectedPeriod(openDetailsId)}
          />
        ) : null;
      })()}
    </div>
  );
};

export default KnowledgeCharts;
