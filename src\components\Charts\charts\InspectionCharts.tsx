import { useState, useMemo } from "react";
import MetricCard from "@/components/Charts/MetricCard";
import MetricDetails from "@/components/Charts/MetricDetails";
import { LocationFilterState } from "@/types/locationFilter";

interface InspectionChartsProps {
  locationFilters?: LocationFilterState;
}

const InspectionCharts = ({ locationFilters }: InspectionChartsProps) => {
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});


  // Inspection metrics data
  const overviewMetrics = [
    {
      id: "findings-identified-as-repeats",
      title: "Percentage of Findings Identified as Repeats",
      value: "15",
      unit: "findings (15%)",
      target: 0,
      targetPercentage: 15,
      trend: [18, 17, 16, 15, 14, 15, 16, 15, 14, 15, 16, 15],
      isImproving: false
    },
    {
      id: "findings-past-closure-due-date",
      title: "Percentage of Inspection Findings Past Their Closure Due Date",
      value: "8",
      unit: "findings (8%)",
      target: 0,
      targetPercentage: 8,
      trend: [12, 11, 10, 9, 8, 7, 8, 9, 8, 7, 8, 8],
      isImproving: false
    },
    {
      id: "rolling-average-days-to-close",
      title: "Rolling Average Days to Close Inspection Findings",
      value: "12.5",
      unit: "days",
      target: 10,
      targetPercentage: 25,
      trend: [15, 14.5, 14, 13.5, 13, 12.5, 13, 12.8, 12.5, 12.2, 12.5, 12.5],
      isImproving: true
    },
    {
      id: "total-inspections-completed",
      title: "Total Number of Inspections Completed",
      value: "142",
      unit: "inspections",
      target: 150,
      targetPercentage: 5.3,
      trend: [125, 128, 132, 135, 138, 140, 142, 140, 138, 140, 142, 142],
      isImproving: true
    }
  ];

  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  return (
    <div className="space-y-6">

      {/* Headline Metrics */}
      <div className="grid grid-cols-4 gap-4">
        {overviewMetrics.map((metric) => (
          <MetricCard
            key={metric.id}
            {...metric}
            showDetails={openDetailsId === metric.id}
            onToggleDetails={() => handleToggleDetails(metric.id)}
            selectedPeriod={getSelectedPeriod(metric.id)}
            onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
          />
        ))}
      </div>

      {/* Details Section - Rendered below the grid when a card is selected */}
      {openDetailsId && (() => {
        const selectedMetric = overviewMetrics.find(m => m.id === openDetailsId);
        return selectedMetric ? (
          <MetricDetails
            title={selectedMetric.title}
            value={selectedMetric.value}
            unit={selectedMetric.unit}
            targetPercentage={selectedMetric.targetPercentage}
            trend={selectedMetric.trend}
            isImproving={selectedMetric.isImproving}
            selectedPeriod={getSelectedPeriod(openDetailsId)}
          />
        ) : null;
      })()}
    </div>
  );
};

export default InspectionCharts;
