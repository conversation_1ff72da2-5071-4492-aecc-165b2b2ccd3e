import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import {
  Activity,
  AlertTriangle,
  Calendar,
  CheckCircle,
  Clock,
  Settings,
  TrendingUp,
  Wrench,
  Zap,
  Gauge,
  BarChart3,
  ArrowUpRight,
  ArrowDownRight,
  Minus
} from 'lucide-react';
import { ATMDashboard as ATMDashboardType } from '@/types/atm';
import { fetchATMDashboard } from '@/services/atmApi';

const ATMDashboard = () => {
  const [dashboardData, setDashboardData] = useState<ATMDashboardType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      const data = await fetchATMDashboard();
      setDashboardData(data);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // For demo purposes, use mock data
      setDashboardData(getMockDashboardData());
      toast({
        title: "Demo Mode",
        description: "Using mock data for demonstration",
        variant: "default"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getMockDashboardData = (): ATMDashboardType => ({
    totalAssets: 156,
    activeAssets: 142,
    assetsUnderMaintenance: 8,
    criticalAssets: 6,
    overdueMaintenances: 3,
    upcomingMaintenances: 12,
    overdueCalibrations: 2,
    upcomingCalibrations: 8,
    activeSensors: 89,
    sensorAlerts: 5,
    totalDowntime: 24.5,
    mtbf: 720,
    mttr: 4.2,
    assetsByCategory: [
      { category: 'HVAC Systems', count: 45, percentage: 28.8 },
      { category: 'Pressure Vessels', count: 32, percentage: 20.5 },
      { category: 'Pumps & Motors', count: 28, percentage: 17.9 },
      { category: 'Safety Equipment', count: 25, percentage: 16.0 },
      { category: 'Instrumentation', count: 26, percentage: 16.7 }
    ],
    maintenanceByType: [
      { type: 'Preventive' as any, count: 18, percentage: 60.0 },
      { type: 'Corrective' as any, count: 8, percentage: 26.7 },
      { type: 'Predictive' as any, count: 3, percentage: 10.0 },
      { type: 'Emergency' as any, count: 1, percentage: 3.3 }
    ],
    downtimeByReason: [
      { reason: 'Planned Maintenance' as any, duration: 12.5, percentage: 51.0 },
      { reason: 'Equipment Failure' as any, duration: 8.0, percentage: 32.7 },
      { reason: 'Unplanned Maintenance' as any, duration: 4.0, percentage: 16.3 }
    ],
    recentAlerts: [],
    upcomingTasks: []
  });

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">No dashboard data available</p>
      </div>
    );
  }

  const getStatusColor = (value: number, threshold: number) => {
    if (value === 0) return 'text-green-600';
    if (value <= threshold) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusIcon = (value: number, threshold: number) => {
    if (value === 0) return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (value <= threshold) return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    return <AlertTriangle className="h-4 w-4 text-red-600" />;
  };

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Assets</p>
                <p className="text-2xl font-bold">{dashboardData.totalAssets}</p>
                <p className="text-xs text-muted-foreground">
                  {dashboardData.activeAssets} active
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {(() => {
                    const now = new Date();
                    const previousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                    return previousMonth.toLocaleDateString('en-US', {
                      month: 'long',
                      year: 'numeric'
                    });
                  })()}
                </p>
              </div>
              <Settings className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Under Maintenance</p>
                <p className="text-2xl font-bold">{dashboardData.assetsUnderMaintenance}</p>
                <p className="text-xs text-muted-foreground">
                  {((dashboardData.assetsUnderMaintenance / dashboardData.totalAssets) * 100).toFixed(1)}% of total
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {(() => {
                    const now = new Date();
                    const previousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                    return previousMonth.toLocaleDateString('en-US', {
                      month: 'long',
                      year: 'numeric'
                    });
                  })()}
                </p>
              </div>
              <Wrench className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Critical Assets</p>
                <p className="text-2xl font-bold text-red-600">{dashboardData.criticalAssets}</p>
                <p className="text-xs text-muted-foreground">
                  Require immediate attention
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {(() => {
                    const now = new Date();
                    const previousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                    return previousMonth.toLocaleDateString('en-US', {
                      month: 'long',
                      year: 'numeric'
                    });
                  })()}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">IoT Sensors</p>
                <p className="text-2xl font-bold">{dashboardData.activeSensors}</p>
                <p className="text-xs text-muted-foreground">
                  {dashboardData.sensorAlerts} active alerts
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {(() => {
                    const now = new Date();
                    const previousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                    return previousMonth.toLocaleDateString('en-US', {
                      month: 'long',
                      year: 'numeric'
                    });
                  })()}
                </p>
              </div>
              <Activity className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Maintenance & Calibration Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Maintenance Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {getStatusIcon(dashboardData.overdueMaintenances, 2)}
                <span className="text-sm">Overdue</span>
              </div>
              <Badge variant={dashboardData.overdueMaintenances > 2 ? "destructive" : "secondary"}>
                {dashboardData.overdueMaintenances}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-600" />
                <span className="text-sm">Upcoming (7 days)</span>
              </div>
              <Badge variant="outline">{dashboardData.upcomingMaintenances}</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gauge className="h-5 w-5" />
              Calibration Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {getStatusIcon(dashboardData.overdueCalibrations, 1)}
                <span className="text-sm">Overdue</span>
              </div>
              <Badge variant={dashboardData.overdueCalibrations > 1 ? "destructive" : "secondary"}>
                {dashboardData.overdueCalibrations}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-600" />
                <span className="text-sm">Upcoming (30 days)</span>
              </div>
              <Badge variant="outline">{dashboardData.upcomingCalibrations}</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Total Downtime</CardTitle>
            <CardDescription>Last 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.totalDowntime}h</div>
            <p className="text-xs text-muted-foreground">
              Average per asset: {(dashboardData.totalDowntime / dashboardData.totalAssets).toFixed(1)}h
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">MTBF</CardTitle>
            <CardDescription>Mean Time Between Failures</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.mtbf}h</div>
            <p className="text-xs text-muted-foreground">
              {(dashboardData.mtbf / 24).toFixed(0)} days average
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">MTTR</CardTitle>
            <CardDescription>Mean Time To Repair</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.mttr}h</div>
            <p className="text-xs text-muted-foreground">
              Average repair time
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Asset Categories Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Asset Distribution by Category</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {dashboardData.assetsByCategory.map((category, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>{category.category}</span>
                  <span className="font-medium">{category.count} ({category.percentage.toFixed(1)}%)</span>
                </div>
                <Progress value={category.percentage} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ATMDashboard;
