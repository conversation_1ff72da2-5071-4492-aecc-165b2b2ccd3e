import { useState, useMemo } from "react";
import MetricCard from "@/components/Charts/MetricCard";
import MetricDetails from "@/components/Charts/MetricDetails";
import { LocationFilterState } from "@/types/locationFilter";

interface IncidentInvestigationChartsProps {
  locationFilters?: LocationFilterState;
}

const IncidentInvestigationCharts = ({ locationFilters }: IncidentInvestigationChartsProps) => {
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});


  // Incident Investigation metrics data
  const overviewMetrics = [
    {
      id: "incidents-reported",
      title: "Number of Incidents Reported",
      value: "24",
      unit: "incidents",
      target: 0,
      targetPercentage: 100,
      trend: [28, 26, 25, 24, 22, 24, 26, 24, 22, 24, 26, 24],
      isImproving: true
    },
    {
      id: "high-severity-incidents",
      title: "% of reported incidents that were of high severity",
      value: "18",
      unit: "incidents (18%)",
      target: 0,
      targetPercentage: 18,
      trend: [22, 21, 20, 19, 18, 17, 18, 19, 18, 17, 18, 18],
      isImproving: true
    },
    {
      id: "overdue-corrective-actions",
      title: "No. of post-incident corrective actions that are overdue for closure",
      value: "6",
      unit: "actions",
      target: 0,
      targetPercentage: 100,
      trend: [10, 9, 8, 7, 6, 5, 6, 7, 6, 5, 6, 6],
      isImproving: true
    },
    {
      id: "recurring-incidents",
      title: "% of reported incidents that are recurring issues",
      value: "12",
      unit: "incidents (12%)",
      target: 0,
      targetPercentage: 12,
      trend: [16, 15, 14, 13, 12, 11, 12, 13, 12, 11, 12, 12],
      isImproving: true
    }
  ];

  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  return (
    <div className="space-y-6">

      {/* Headline Metrics */}
      <div className="grid grid-cols-4 gap-4">
        {overviewMetrics.map((metric) => (
          <MetricCard
            key={metric.id}
            {...metric}
            showDetails={openDetailsId === metric.id}
            onToggleDetails={() => handleToggleDetails(metric.id)}
            selectedPeriod={getSelectedPeriod(metric.id)}
            onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
          />
        ))}
      </div>

      {/* Details Section - Rendered below the grid when a card is selected */}
      {openDetailsId && (() => {
        const selectedMetric = overviewMetrics.find(m => m.id === openDetailsId);
        return selectedMetric ? (
          <MetricDetails
            title={selectedMetric.title}
            value={selectedMetric.value}
            unit={selectedMetric.unit}
            targetPercentage={selectedMetric.targetPercentage}
            trend={selectedMetric.trend}
            isImproving={selectedMetric.isImproving}
            selectedPeriod={getSelectedPeriod(openDetailsId)}
          />
        ) : null;
      })()}
    </div>
  );
};

export default IncidentInvestigationCharts;
