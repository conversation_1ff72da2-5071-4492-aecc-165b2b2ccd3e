import React, { useEffect, useState, useRef } from 'react';
import { useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { MultiSelect } from '@/components/ui/multi-select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';

// Icons
import { Plus, Trash2, Upload, Save, Send, FileText, Building, Users, ClipboardList, AlertTriangle, ChevronDown, ChevronUp } from 'lucide-react';

// Services and Utils
import apiService from '@/services/apiService';
import { RootState } from '@/store';
import { API_BASE_URL } from '@/constants/index';

// Components
import MultiFileUpload from '@/components/common/MultiFileUpload';
import ImageComponent from '@/components/common/ImageComponent';
import SignatureCanvas from 'react-signature-canvas';
import HazardAccordion from '@/components/risk-assessment/Component/Hazards/HazardAccordion';
import IdentifiedHazards from '@/components/risk-assessment/Component/Hazards/IdentifiedHazards';
import Consequence from '@/components/risk-assessment/Component/Consequence/Consequence';
import CurrentControl from '@/components/risk-assessment/Component/CurrentControl/CurrentControl';
import RiskAssessment from '@/components/risk-assessment/Component/RiskAssessment/RiskAssessment';
import ProposedRiskManagement from '@/components/risk-assessment/Component/AdditionalProposed/RiskManagement';
import TaskItemComponent from '@/components/risk-assessment/Component/TaskItem';
import RiskUpdate from '@/components/risk-assessment/Component/RiskUpdate';
import UpdateTable from '@/components/risk-assessment/Component/UpdateTable';
import HeadStepper from '@/components/risk-assessment/Component/HeadStepper';
import SubActivityComponent from '@/components/risk-assessment/Component/SubActivityComponent';

// TypeScript Interfaces
interface Department {
  label: string;
  value: string;
}

interface WorkActivity {
  id: string;
  name: string;
}

interface User {
  id: string;
  firstName: string;
  lastName?: string;
  email?: string;
}

interface TeamMember {
  id: string;
  name: string;
}

interface HazardItem {
  id: string;
  name: string;
  image?: string;
  hazardName?: string;
}

interface TaskOption {
  value: string;
  files: string[];
  current_type: string;
  method?: string;
  owner?: string;
  person?: string;
  date?: Date | null;
  required?: boolean;
  validity?: boolean;
}

interface TaskStatus {
  hazardsIdentification: string;
  consequences: string;
  currentControls: string;
  riskEstimation: string;
  additionalControls: string;
}

interface TaskItem {
  type: string;
  name?: string;
  images?: string[];
  selected?: HazardItem[];
  option?: TaskOption[];
  severity?: string;
  likelyhood?: string;
  level?: string | string[] | number[] | any;
  accept?: boolean;
  step?: number;
  value?: TaskStatus | string[] | number | any;
}

interface Recommendation {
  label: string;
  value: string;
}

interface RiskAssessmentData {
  id: string;
  departmentId: string;
  workActivityId: string;
  status: string;
  teamLeaderDeclaration: {
    name: string;
    sign: string;
  };
  raTeamMembers: TeamMember[];
  tasks: TaskItem[][];
  additonalRemarks?: string;
  overallRecommendationOne?: Recommendation;
  overallRecommendationTwo?: Recommendation;
  highRisk?: HazardItem[];
  nonRoutineDepartment?: string;
  nonRoutineWorkActivity?: string;
  hazardName?: string;
  description?: string;
  shortName?: string;
}

interface RiskAssessmentFormProps {
  data?: RiskAssessmentData | null;
  type?: 'routine' | 'nonroutine' | 'highrisk';
  domain?: 'new' | 'edit';
}

const RiskAssessmentForm: React.FC<RiskAssessmentFormProps> = ({
  data = null,
  type,
  domain
}) => {

  console.log(type, domain, data)
  // API Constants
  const GMS1_URL = '/departments';
  const GET_USER_ROLE_BY_MODE = '/users/get_users';
  const HAZARDS_CATEGOTY = 'https://risk-api.acuizen.com/hazards-categories';
  const SENT_NOTIFICATION_MAIL = '/risk-member-notification';
  const RISK_UPDATE_WITH_ID_URL = (id: string) => `/risk-assessments/${id}/risk-updates`;
  const GET_ALL_USER = '/users';
  const FILE_URL = '/files';
  const RISKASSESSMENT_LIST = '/risk-assessments';
  const GET_RISK_WITH_ID_URL = (id: string) => `/risk-assessments/${id}`;
  const RISK_WITH_ID_URL = (id: string) => `/risk-assessments-update/${id}`;
  const DRAFT_RA = '/risk-assessments-draft';
  const RISK_UPDATE_DRAFT_WITH_ID = (id: string) => `/risk-assessments-update-draft/${id}`;
  const GET_RISK_HAZARD_URL = 'https://svioxrm.acuizen.com/hazard-industries';


  const user = useSelector((state: RootState) => state.auth.user);
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const signRef = useRef<SignatureCanvas>(null);

  // State with proper TypeScript types
  const [files, setFiles] = useState<File[]>([]);
  const [depart, setDepart] = useState<Department[]>([]);
  const [activity, setActivity] = useState<{ label: string; value: string }[]>([]);
  const [crew, setCrew] = useState<TeamMember[]>([]);
  const [selectedDepart, setSelectedDepart] = useState<Department | null>(null);
  const [selectedActivity, setSelectedActivity] = useState<{ label: string; value: string } | null>(null);
  const [selectedCrew, setSelectedCrew] = useState<TeamMember[]>([]);
  const [addSubActivity, setAddSubActivity] = useState<boolean>(false);
  const [activityDesc, setActivityDesc] = useState<string>('');
  const [task, setTask] = useState<TaskItem[][]>([]);
  const [subActivityName, setSubActivityName] = useState<string>('');
  const [visible, setVisible] = useState<boolean>(false);
  const [item, setItem] = useState<TaskItem[] | null>(null);
  const [index, setIndex] = useState<number>(0);
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const [hazards, setHazards] = useState<any[]>([]);
  const [activeTabIndex, setActiveTabIndex] = useState<number>(0);
  const [currentTab, setCurrentTab] = useState<string>('general');
  const [severityTable, setSeverityTable] = useState<boolean>(false);
  const [likelyhoodTable, setLikelyhoodTable] = useState<boolean>(false);
  const [riskTable, setRiskTable] = useState<boolean>(false);
  const [responsibility, setResponsibility] = useState<User[]>([]);
  const [required, setRequired] = useState<boolean>(true);
  const [recommendationOne, setRecommendationOne] = useState<{ label: string; value: string } | null>(null);
  const [recommendationTwo, setRecommendationTwo] = useState<{ label: string; value: string } | null>(null);
  const [nonRoutineDepartment, setNonRoutineDepartment] = useState<string>('');
  const [nonRoutineActivity, setNonRoutineActivity] = useState<string>('');
  const [additionalRecommendation, setAdditionalRecommendation] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [hazardName, setHazardName] = useState<string>('');
  const [risk, setRisk] = useState<HazardItem[]>([]);
  const [eptwHot, setEptwHot] = useState<HazardItem[]>([]);
  const [raTeamMember, setRATeamMember] = useState<any[]>([]);
  const [subActivityModal, setSubActivityModal] = useState<boolean>(false);
  const [riskUpdate, setRiskUpdate] = useState<boolean>(false);
  const [Update, setUpdate] = useState<any[]>([]);
  const [draggedItemIndex, setDraggedItemIndex] = useState<number | null>(null);
  const [riskRoutine, setRiskRoutine] = useState<any[]>([]);
  const [onEdit, setOnEdit] = useState<boolean>(false);
  const [shortName, setShortName] = useState<string>('');
  const [isHazardsCollapsed, setIsHazardsCollapsed] = useState<boolean>(true);
  const severity = [
    { "value": "1", "label": "1(E) - Negligible" },
    { "value": "2", "label": "2(D) - Minor" },
    { "value": "3", "label": "3(C) - Moderate" },
    { "value": "4", "label": "4(B) - Major" },
    { "value": "5", "label": "5(A) - Catastrophic" }
  ]
  const likelyhood = [
    { label: "Rare (1)", value: "1" },
    { label: "Unlikely (2)", value: "2" },
    { label: "Possible (3)", value: "3" },
    { label: "Likely (4)", value: "4" },
    { label: "Almost Certain (5)", value: "5" },
  ]
  const impactOn = [
    { 'label': 'Personnel', 'value': 'Personnel' },
    { 'label': 'Environment', 'value': 'Environment' },
    { 'label': 'Property / Equipment', 'value': 'Property / Equipment' },
    { 'label': 'Operations', 'value': 'Operations' },
  ]
  const control = [
    { 'label': 'No Control', 'value': 'No Control' },
    { 'label': 'Engineering', 'value': 'Engineering' },
    { 'label': 'Administrative', 'value': 'Administrative' },
    { 'label': 'PPE', 'value': 'PPE' }

  ]

  const controlAdditional = [
    { 'label': 'Elimination', 'value': 'Elimination' },
    { 'label': 'Substitution', 'value': 'Substitution' },
    { 'label': 'Engineering', 'value': 'Engineering' },
    { 'label': 'Administrative', 'value': 'Administrative' },
    { 'label': 'PPE', 'value': 'PPE' }

  ]

  const controlType = [
    { 'label': 'Preventative', 'value': 'Preventative' },
    { 'label': 'Mitigative', 'value': 'Mitigative' },
    { 'label': 'Preventative & Mitigative', 'value': 'Preventative & Mitigative' }

  ]

  const severityData = [
    {
      id: '5 (A)',
      severity: 'Catastrophic',
      personnel: 'Serious injury with long-term or permanent disability or death.',
      property: 'Significant damage leading to major repairs.',
      environment: 'Significant environmental damage requiring regulatory reporting and cleanup.',
      serviceLoss: 'Major disruption to service operations, extended recovery time.'
    },
    {
      id: '4 (B)',
      severity: 'Major',
      personnel: 'Serious injury with long-term recovery or permanent disability.',
      property: 'Noticeable damage requiring repair, causing moderate downtime or repair costs.',
      environment: 'Moderate, recoverable environmental impact, requiring external agency notification or cleanup.',
      serviceLoss: 'Significant downtime with substantial recovery efforts.'
    },
    {
      id: '3 (C)',
      severity: 'Moderate',
      personnel: 'Injury requiring medical treatment with potential for short-term lost workdays or restricted duties.',
      property: 'Noticeable damage requiring repair, causing moderate downtime or repair costs.',
      environment: 'Moderate environmental impact, manageable on-site with potential regulatory notification.',
      serviceLoss: 'Moderate service interruption with short recovery.'
    },
    {
      id: '2 (D)',
      severity: 'Minor',
      personnel: 'Minor injury requiring first-aid or outpatient treatment, with minimal lost time.',
      property: 'Slight damage requiring minor repairs without significant downtime.',
      environment: 'Small localized impact, manageable on-site, without long-term environmental damage.',
      serviceLoss: 'Brief disruption to services, easily restored.'
    },
    {
      id: '1 (E)',
      severity: 'Insignificant',
      personnel: 'Minor first-aid required with no lost time or long-term health impacts.',
      property: 'Minimal damage or wear that does not require repair or interruption to operations.',
      environment: 'Negligible environmental impact with no regulatory involvement needed.',
      serviceLoss: 'No impact on services.'
    }
  ];

  const levelData = [
    {
      level: '1',
      descriptor: 'Rare',
      detailedDescription: 'The event is highly unlikely to occur under normal circumstances, with little to no historical precedent.'
    },
    {
      level: '2',
      descriptor: 'Unlikely',
      detailedDescription: 'The event is improbable but could potentially happen under unusual conditions, though there is limited historical data to support this.'
    },
    {
      level: '3',
      descriptor: 'Possible',
      detailedDescription: 'The event could happen, with moderate chances of occurring based on historical records or foreseeable conditions.'
    },
    {
      level: '4',
      descriptor: 'Likely',
      detailedDescription: 'The event is expected to occur in the normal course of operations, with a significant history of similar incidents.'
    },
    {
      level: '5',
      descriptor: 'Almost Certain',
      detailedDescription: 'The event is highly likely to occur and is anticipated in the near future without further intervention.'
    }
  ];

  const tableData = [
    { id: '5(A)', severity: 'Catastrophic', rare: '5(A)', unlikely: '10(A)', possible: '15(A)', likely: '20(A)', almostCertain: '25(A)' },
    { id: '4(B)', severity: 'Major', rare: '4(B)', unlikely: '8(B)', possible: '12(B)', likely: '16(B)', almostCertain: '20(B)' },
    { id: '3(C)', severity: 'Moderate', rare: '3(C)', unlikely: '6(C)', possible: '9(C)', likely: '12(C)', almostCertain: '15(C)' },
    { id: '2(D)', severity: 'Minor', rare: '2(D)', unlikely: '4(D)', possible: '6(D)', likely: '8(D)', almostCertain: '10(D)' },
    { id: '1(E)', severity: 'Insignificant', rare: '1(E)', unlikely: '2(E)', possible: '3(E)', likely: '4(E)', almostCertain: '5(E)' },
  ];



  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch all data asynchronously in parallel
        await Promise.all([
          getRoutineList(),
          getDepartments(),
          getWorkActivities(),
          getCrewList(),
          getHazardList(),
          getAllResponsibility(),
          getHighRiskHazardList()
        ]);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: "Error",
          description: "Failed to load initial data",
          variant: "destructive",
        });
      }
    };

    fetchData(); // Call the async function to execute all requests
  }, []);


  const onSubmitUpdate = () => {
    getRiskUpdate();
    editUserHandler();

  }

  useEffect(() => {

    setOnEdit(true)

  }, [domain == "edit"])

  const handleDragStart = (event, index) => {
    setDraggedItemIndex(index);
    event.dataTransfer.effectAllowed = 'move';
  };

  // Handles the drop event
  const handleDrop = (event, dropIndex) => {
    event.preventDefault();
    const draggedItem = task[draggedItemIndex];
    const updatedTaskList = [...task];

    // Remove the dragged item from its current position
    updatedTaskList.splice(draggedItemIndex, 1);

    // Insert it at the new position
    updatedTaskList.splice(dropIndex, 0, draggedItem);

    // Update the state with the reordered task list
    setTask(updatedTaskList);
    setDraggedItemIndex(null);
  };

  // Prevents the default drag over behavior
  const handleDragOver = (event) => {
    event.preventDefault();
  };
  useEffect(() => {
    if (onEdit === true && depart.length !== 0 && activity.length !== 0 && domain === 'edit' && data) {
      // Find and set the selected department based on departmentId
      const defaultDepartment = depart.find(option => option.value === data.departmentId);
      if (defaultDepartment) {
        console.log('Setting selected department:', defaultDepartment);
        setSelectedDepart(defaultDepartment);
      } else {
        console.error("Department not found for the given departmentId:", data.departmentId);
      }

      // Find and set the selected work activity based on workActivityId
      const defaultActivity = activity.find(option => option.value === data.workActivityId);
      if (defaultActivity) {
        console.log('Setting selected activity:', defaultActivity);
        setSelectedActivity(defaultActivity);
      } else {
        console.error("Work activity not found for the given workActivityId:", data.workActivityId);
      }

      // Reset onEdit flag
      setOnEdit(false);
    }
  }, [onEdit, depart, activity, domain, data]);



  useEffect(() => {
    const fetchData = async () => {
      const uriString = {
        include: [
          { relation: "department" },
          { relation: "teamLeader" },
          { relation: "workActivity" },
          {
            relation: "raTeamMembers",
            scope: {
              include: [{ relation: "user" }]
            }
          }
        ]
      };

      const url = `${GET_RISK_WITH_ID_URL(data.id)}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;
      try {
        const response = await apiService.get(url);
        setTask(response.tasks);
        setAdditionalRecommendation(response.additonalRemarks);
        setRecommendationOne(response.overallRecommendationOne);
        setRecommendationTwo(response.overallRecommendationTwo);
        setSelectedCrew(response.raTeamMembers.map((option: any) => ({
          name: option.user?.firstName || 'Unknown',
          id: option.user?.id || '',
        })));
        setActivityDesc(response.description);
        setNonRoutineActivity(response.nonRoutineWorkActivity);
        setNonRoutineDepartment(response.nonRoutineDepartment);
        setHazardName(response.hazardName);
        setEptwHot(response.highRisk);
        setRATeamMember(response.raTeamMembers);
        setShortName(response.shortName);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    if (data) {
      fetchData();
      getRiskUpdate();
    }

  }, [domain]);
  const getRiskUpdate = async () => {
    if (!data?.id) return;

    try {
      const response = await apiService.get(RISK_UPDATE_WITH_ID_URL(data.id));
      setUpdate(response);
    } catch (error) {
      console.error("Error fetching risk update:", error);
      toast({
        title: "Error",
        description: "Failed to fetch risk updates",
        variant: "destructive",
      });
    }
  };

  const getHighRiskHazardList = async () => {
    // Define the filter criteria
    const uriString = {
      where: {
        $and: [
          {
            $or: [
              { status: 'Pending' },
              { status: 'Draft' },
              { status: 'Published' }
            ]
          },
          {
            $or: [
              { type: 'High-Risk Hazard' }
            ]
          }
        ]
      },
      fields: { id: true, hazardName: true }
    };

    // Construct the URL with the encoded filter
    const url = `${RISKASSESSMENT_LIST}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;

    try {
      // Make the GET request to the API
      const response = await apiService.get(url);
      setRisk(response);
    } catch (error) {
      console.error('Error fetching high-risk hazard list:', error);
      toast({
        title: "Error",
        description: "Failed to fetch high-risk hazard list",
        variant: "destructive",
      });
    }
  };

  const getRoutineList = async () => {
    // Define the filter criteria
    const uriString = {
      where: {
        $and: [
          {
            $or: [
              { status: 'Pending' },
              { status: 'Draft' },
              { status: 'Published' }
            ]
          },
          {
            $or: [
              { type: 'Routine' }
            ]
          }
        ]
      },
      fields: { workActivityId: true }
    };

    // Construct the URL with the encoded filter
    const url = `${RISKASSESSMENT_LIST}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;

    try {
      // Make the GET request to the API
      const response = await apiService.get(url);
      setRiskRoutine(response);
    } catch (error) {
      console.error('Error fetching routine list:', error);
      toast({
        title: "Error",
        description: "Failed to fetch routine list",
        variant: "destructive",
      });
    }
  };

  const getAllResponsibility = async () => {
    const uriString = {
      include: [
        { "relation": "workingGroup" },
        { "relation": "designation" },
        { "relation": "department" }
      ]
    };
    const url = `${GET_ALL_USER}?filter=${encodeURIComponent(
      JSON.stringify(uriString)
    )}`;

    try {
      const response = await apiService.get(url);
      const users = response.map((item: any) => ({
        id: item.id,
        firstName: item.firstName,
        email: item.email
      }));
      setResponsibility(users);
    } catch (error) {
      console.error('Error fetching responsibility users:', error);
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive",
      });
    }
  };
  const getHazardList = async () => {
    const selectedIndustry = localStorage.getItem('SELECTED_INDUSTRIES');

    if (selectedIndustry) {
      const selectedIndustryNames = selectedIndustry
        .split(',')
        .map(name => name.trim());

      const uriString = {
        include: [
          {
            relation: "hazardCategories",
            scope: {
              include: [{ relation: "hazardItems" }]
            }
          }
        ]
      };

      const url = `${GET_RISK_HAZARD_URL}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      try {
        const response = await apiService.get(url);
        const industryList = response;

        const matchedIndustries = industryList.filter((item: any) =>
          selectedIndustryNames.includes(item.name)
        );

        // Combine all hazardCategories from matched industries
        const allHazards = matchedIndustries.flatMap(
          (industry: any) => industry.hazardCategories || []
        );

        setHazards(allHazards);
      } catch (err) {
        console.error('Error fetching industry-based hazards:', err);
        toast({
          title: "Error",
          description: "Failed to fetch industry hazards",
          variant: "destructive",
        });
      }
    } else {
      // Default hazard category fallback
      const uriString = { include: ["hazards"] };
      const url = `${HAZARDS_CATEGOTY}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      try {
        const response = await apiService.get(url);
        const filteredData = response.filter(
          (item: any) => item.name !== 'Hazard-Based'
        );
        setHazards(filteredData);
      } catch (err) {
        console.error('Error fetching default hazard categories:', err);
        toast({
          title: "Error",
          description: "Failed to fetch hazard categories",
          variant: "destructive",
        });
      }
    }
  };

  const handleFileChange = async (files) => {

    setFiles(files)

  };
  const getDepartments = async () => {
    try {
      const response = await apiService.get(`${GMS1_URL}`);
      const transformedOptions = response.map((option: any) => ({
        label: option.name,
        value: option.id,
      }));
      setDepart(transformedOptions);
    } catch (error) {
      console.error('Error fetching departments:', error);
      toast({
        title: "Error",
        description: "Failed to fetch departments",
        variant: "destructive",
      });
    }
  };

  const getWorkActivities = async () => {
    try {
      const response = await apiService.get('/work-activities');
      const transformedOptions = response.map((option: any) => ({
        label: option.name,
        value: option.id,
      }));
      setActivity(transformedOptions);
    } catch (error) {
      console.error('Error fetching work activities:', error);
      toast({
        title: "Error",
        description: "Failed to fetch work activities",
        variant: "destructive",
      });
    }
  };



  const getCrewList = async () => {
    try {
      const response = await apiService.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: "",
        locationTwoId: "",
        locationThreeId: "",
        locationFourId: "",
        mode: 'ra_member'
      });

      const crewData: TeamMember[] = [];
      response.forEach((item: any) => {
        if (item.id !== user?.id) {
          crewData.push({ name: item.firstName, id: item.id });
        }
      });

      setCrew(crewData);
    } catch (error) {
      console.error('Error fetching crew list:', error);
      toast({
        title: "Error",
        description: "Failed to fetch team members",
        variant: "destructive",
      });
    }
  };

  const AddSubActivityTitle = async () => {
    const uploadedImages: string[] = [];

    if (files.length !== 0) {
      for (const file of files) {
        const formData = new FormData();
        formData.append('file', file);

        try {
          const response = await apiService.post(FILE_URL, formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
            }
          });

          if (response?.files?.[0]?.originalname) {
            uploadedImages.push(response.files[0].originalname);
          }
        } catch (error) {
          console.error("File upload error: ", error);
          toast({
            title: "Error",
            description: "Failed to upload file",
            variant: "destructive",
          });
        }
      }
    }

    if (subActivityName.trim() !== '') {
      const newTask: TaskItem[] = [
        { type: 'activity', name: subActivityName, images: uploadedImages },
        { type: 'hazards', selected: [] },
        { type: 'consequence', option: [{ value: "", files: [], current_type: '' }] },
        { type: 'current_control', option: [{ value: "", files: [], current_type: '', method: '', owner: '' }] },
        { type: 'assessment', severity: '', likelyhood: '', level: '' },
        { type: 'additional', accept: true },
        { type: 'responsibility', option: [{ current_type: '', person: '', date: null, value: '', files: [] }] },
        { type: 'reassessment', severity: '', likelyhood: '', level: '' },
        { type: 'activeStep', step: 0 },
        { type: 'stage', level: ['Hazards Identification', 'Consequences', 'Current Controls', 'Risk Estimation'] },
        { type: 'completed_stage', level: [] },
        {
          type: 'status', value: {
            hazardsIdentification: '',
            consequences: '',
            currentControls: '',
            riskEstimation: '',
            additionalControls: '',
          }
        }
      ];

      setTask((prev) => [...prev, newTask]);
      setSubActivityName('');
      setFiles([]);
      setAddSubActivity(false);

      toast({
        title: "Success",
        description: "Sub-activity added successfully",
      });
    }
  };

  const handleMainImage = (m) => {
    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'activity') {
            ite.images.splice(m, 1)

          }
        })
      }
      return item
    }
    )
    setTask(text)
    setItem(text[index])
  }
  const deleteTask = (e, i) => {
    e.stopPropagation()
    const t = task;
    const newTasks = task.filter((_, idx) => idx !== i);
    setTask(newTasks);
  }
  const openDialog = (item, i) => {
    setItem(null)
    setItem(item);
    setIndex(i)
    setVisible(true)

  }
  const subActivity = (item, i) => {
    setItem(null)
    setItem(item);
    setIndex(i)
    setSubActivityModal(true)
  }

  const checkRequiredStepField = () => {
    const currentStep = item[8]?.step;
    let required = true;

    // Only validate the current step - no other steps
    if (currentStep === 0) {
      // Step 0: Hazards Identification
      if (!item[1]?.selected || item[1].selected.length === 0) {
        required = false;
        setRequired(false);
      }
    } else if (currentStep === 1) {
      // Step 1: Consequences
      if (!item[2]?.option || item[2].option.length === 0) {
        required = false;
        setRequired(false);
      } else {
        for (let option of item[2].option) {
          if (!option.value || option.value.trim() === '' || !option.current_type || option.current_type.trim() === '') {
            required = false;
            setRequired(false);
            break;
          }
        }
      }
    } else if (currentStep === 2) {
      // Step 2: Current Controls
      if (!item[3]?.option || item[3].option.length === 0) {
        required = false;
        setRequired(false);
      } else {
        for (let option of item[3].option) {
          if (!option.value || option.value.trim() === '' || !option.current_type || option.current_type.trim() === '' || !option.method || option.method.trim() === '' || !option.owner || option.owner.trim() === '') {
            required = false;
            setRequired(false);
            break;
          }
        }
      }
    } else if (currentStep === 3) {
      // Step 3: Risk Estimation
      if (!item[4]?.severity || item[4].severity === '' || !item[4]?.likelyhood || item[4].likelyhood === '') {
        required = false;
        setRequired(false);
      }
    } else if (currentStep === 4) {
      // Step 4: Additional Controls
      if (!item[7]?.severity || item[7].severity === '' || !item[7]?.likelyhood || item[7].likelyhood === '') {
        required = false;
        setRequired(false);
      }
    }

    return required;
  };

  const headerTemplate = (
    <div className="d-flex flex-column">
      <div className='col-12 '>
        Assessment

      </div>

    </div>
  );
  const handleNext = () => {
    console.log(checkRequiredStepField());
    if (checkRequiredStepField()) {
      // Add current step to completed_stage and ensure no duplicates
      item[10].level.push(item[8].step);
      item[10].level = [...new Set(item[10].level)];

      // Check if the last step is reached
      // if (item[8].step === item[9].level.length - 1) {
      //     setVisible(false);
      // } else {
      // Update task array with status
      const updatedTask = task.map((item, i) => {
        if (i === index) {
          item.map((ite) => {
            // if (ite.type === 'activeStep') {
            //     ite.step = ite.step + 1; // Move to the next step
            // }

            if (ite.type === 'status') {
              // Update status based on the active step
              switch (item[8].step) {
                case 0:
                  ite.value.hazardsIdentification = 'completed';
                  break;
                case 1:
                  ite.value.consequences = 'completed';
                  break;
                case 2:
                  ite.value.currentControls = 'completed';
                  break;
                case 3:
                  ite.value.riskEstimation = 'completed';
                  break;
                case 4:
                  ite.value.additionalControls = 'completed';
                  break;
                default:
                  break;
              }
            }
          });
        }
        return item;
      });

      // Update state with the modified task and item
      setTask(updatedTask);
      setItem(updatedTask[index]);

      if (item && item[8] && typeof item[8].step !== 'undefined') {
        const currentSectionName = sectionNames[item[8].step];
        toast({
          title: "Saved & Finalized",
          description: `${currentSectionName} saved & finalized.`,
        });
      }
      // }
    } else {
      alert('Please fill in the required fields');
    }
  };
  const saveProgress = () => {



    const updatedTask = task.map((item, i) => {
      if (i === index) {
        item.map((ite) => {

          if (ite.type === 'status') {
            // Update status based on the active step
            switch (item[8].step) {
              case 0:
                ite.value.hazardsIdentification = 'inprogress';
                break;
              case 1:
                ite.value.consequences = 'inprogress';
                break;
              case 2:
                ite.value.currentControls = 'inprogress';
                break;
              case 3:
                ite.value.riskEstimation = 'inprogress';
                break;
              case 4:
                ite.value.additionalControls = 'inprogress';
                break;
              default:
                break;
            }
          }
        });
      }
      return item;
    });

    // Update state with the modified task and item
    setTask(updatedTask);
    setItem(updatedTask[index]);

    if (item && item[8] && typeof item[8].step !== 'undefined') {
      const currentSectionName = sectionNames[item[8].step];
      toast({
        title: "Saved as Draft",
        description: `${currentSectionName} saved as draft.`,
      });
    }
  };

  const handleBack = () => {

    item[10].level = item[10].level.filter(item1 => item1 !== item[8].step);
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'activeStep') {
            ite.step = ite.step - 1

          }
          if (ite.type === 'completed_stage') {
            ite.level.pop(ite.step)
          }
        })
      }
      return item
    })
    setTask(text)
    setItem(text[index])


  };

  const handleStageClick = (step) => {

    item[10].level = item[10].level.filter(item1 => item1 !== item[8].step);
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'activeStep') {
            ite.step = step
          }

        })
      }
      return item
    })
    setTask(text)
    setItem(text[index])


  };
  const sectionNames = [
    "Hazard Identification",
    "Consequences",
    "Current Controls",
    "Risk Estimation",
    "Additional Controls"
  ];
  const footerTemplate = (
    <div className="d-flex justify-content-between align-items-center">
      {/* Safely access item[8] and item[8].step */}
      {item && item[8] && typeof item[8].step !== 'undefined' && (
        <>



          <div className="flex gap-2">
            {/* Save Progress button with section name */}
            <Button
              variant="outline"
              onClick={saveProgress}
              className="mr-2"
            >
              <Save className="w-4 h-4 mr-2" />
              Save Progress
            </Button>
            {/* Save & Finalize button with section name */}
            <Button
              onClick={handleNext}
            >
              <Send className="w-4 h-4 mr-2" />
              Save & Finalize {sectionNames[item[8].step]} for Sub Activity
            </Button>
          </div>
        </>
      )}
    </div>
  );

  const rowClassName = (data) => {
    switch (data.level[0]) {
      case '1':
        return 'bg-green-50 hover:bg-green-100 border-green-200';
      case '2':
        return 'bg-blue-50 hover:bg-blue-100 border-blue-200';
      case '3':
        return 'bg-yellow-50 hover:bg-yellow-100 border-yellow-200';
      case '4':
        return 'bg-orange-50 hover:bg-orange-100 border-orange-200';
      case '5':
        return 'bg-red-50 hover:bg-red-100 border-red-200';
      default:
        return 'bg-gray-50 hover:bg-gray-100 border-gray-200';
    }
  };
  const cellClassName = (value) => {
    const numericValue = parseInt(String(value).replace(/[^\d]/g, ''), 10);

    // Check if the numeric value is 0
    if (numericValue === 0) return 'bg-gray-100 text-gray-500';

    // Low risk (green)
    if (numericValue === 1 || numericValue === 2 || numericValue === 3 || numericValue === 4) {
      return 'bg-green-100 text-green-800 border-green-200';
    }

    // High risk (red)
    if (numericValue === 15 || numericValue === 20 || numericValue === 25 || numericValue === 16) {
      return 'bg-red-100 text-red-800 border-red-200';
    }

    // Medium risk (yellow/orange)
    return 'bg-yellow-100 text-yellow-800 border-yellow-200';
  };

  const cellStyle = (data, field) => cellClassName(data[field]);

  const onClickHazards = (ha, j) => {
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'hazards') {
            if (ite.selected.some(hazards => hazards.id === ha.id)) {
              const index = ite.selected.findIndex(hazard => hazard.id === ha.id);
              if (index !== -1) {
                const newHazards = [...ite.selected];
                newHazards.splice(index, 1);
                ite.selected = newHazards;
              }
            } else {
              ite.selected.push(ha)
            }
          }
        })
      }
      return item
    })
    setTask(text)
    setItem(text[index])
  }
  const onChangeSeverity = (e, type) => {

    console.log(e, type)
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === type) {

            ite.severity = e.value

          }
        })
      }
      return item
    })
    setTask(text)
    setItem(text[index])
  }
  const onChangeLikelyhood = (e, type) => {
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === type) {

            ite.likelyhood = e.value

          }
        })
      }
      return item
    })
    setTask(text)
    setItem(text[index])
  }
  const onDeleteHaz = (item1) => {
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'hazards') {
            const index = ite.selected.findIndex(hazard => hazard.id === item1.id);
            if (index !== -1) {
              const newHazards = [...ite.selected];
              newHazards.splice(index, 1);
              ite.selected = newHazards;
            }
          }
        })
      }
      return item
    })
    setTask(text)
    setItem(text[index])
  }
  const onImapactOn = (value, j, type) => {
    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === type) {

            ite.option.map((con, c) => {
              if (c === j) {
                con.current_type = value
              }


            })

          }
        })
      }
      return item
    }
    )
    setTask(text)
    setItem(text[index])
  }

  const onMethodOn = (value, j, type) => {
    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === type) {

            ite.option.map((con, c) => {
              if (c === j) {
                con.method = value
              }


            })

          }
        })
      }
      return item
    }
    )
    setTask(text)
    setItem(text[index])
  }

  const onOwnerChange = (value, j, type) => {
    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === type) {
            ite.option.map((con, c) => {
              if (c === j) {
                con.owner = value
              }
            })
          }
        })
      }
      return item
    })
    setTask(text)
    setItem(text[index])
  }

  const onControlAddion = (value, j) => {

    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'responsibility') {
            ite.option.map((con, c) => {
              if (c === j) {
                con.current_type = value
              }
            })

          }
        })
      }
      return item
    }
    )
    setTask(text)
    setItem(text[index])
  }
  const onControlAddionText = (value, j) => {

    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'responsibility') {
            ite.option.map((con, c) => {
              if (c === j) {
                con.value = value
              }
            })

          }
        })
      }
      return item
    }
    )
    setTask(text)
    setItem(text[index])
  }
  const onResponsePerson = (value, j) => {

    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'responsibility') {
            ite.option.map((con, c) => {
              if (c === j) {
                con.person = value
              }
            })

          }
        })
      }
      return item
    }
    )
    setTask(text)
    setItem(text[index])
  }
  const onResponseDate = (value, j) => {

    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'responsibility') {
            ite.option.map((con, c) => {
              if (c === j) {
                con.date = value
              }
            })

          }
        })
      }
      return item
    }
    )
    setTask(text)
    setItem(text[index])
  }
  const onChangeReAss = (value) => {
    console.log(value);
    const t = task; // Create a copy of the task array

    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'additional') {
            ite.accept = value;
          }
          if (ite.type === 'stage') {
            if (value === false) {
              // Push "Additional Controls" only if it does not already exist
              if (!ite.level.includes('Additional Controls')) {
                ite.level.push('Additional Controls');
              }
            } else {
              ite.level = ite.level.filter(item => item !== 'Additional Controls');
            }
          }
        });
      }
      return item;
    });

    setTask(text);
    setItem(text[index]);
  }

  const onConseqText = (value, j, type) => {
    console.log(value)
    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === type) {
            ite.option.map((con, c) => {
              if (c === j) {
                con.value = value
              }

            })

          }
        })
      }
      return item
    }
    )
    setTask(text)
    setItem(text[index])
  }

  const onConseqRequired = (value, j, type, type1) => {
    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === type) {
            ite.option.map((con, c) => {
              if (c === j) {
                if (type1 === 'required') {
                  con.required = !value
                } else {
                  con.validity = !value
                }

              }

            })

          }
        })
      }
      return item
    }
    )

    console.log(text)
    setTask(text)
    setItem(text[index])
  }

  const handleTaskFileChange = async (value, j, type) => {
    if (value.length > 0) {
      const uploadedFileNames = [];

      // Upload new files
      for (const file of value) {
        const formData1 = new FormData();
        formData1.append('file', file);

        try {
          const response = await apiService.post(FILE_URL, formData1, {
            headers: {
              'Content-Type': 'multipart/form-data',
            }
          });

          if (response && response.files && response.files.length > 0) {
            uploadedFileNames.push(response.files[0].originalname);
          }
        } catch (error) {
          console.error("File upload error: ", error);
          toast({
            title: "Error",
            description: `Failed to upload ${file.name}`,
            variant: "destructive",
          });
        }
      }

      // Update state by adding new files to existing ones
      if (uploadedFileNames.length > 0) {
        const t = task;
        const text = t.map((item, i) => {
          if (i === index) {
            return item.map((ite) => {
              if (ite.type === type) {
                return {
                  ...ite,
                  option: ite.option.map((con, c) => {
                    if (c === j) {
                      return {
                        ...con,
                        files: [...(con.files || []), ...uploadedFileNames]
                      };
                    }
                    return con;
                  })
                };
              }
              return ite;
            });
          }
          return item;
        });

        setTask(text);
        setItem(text[index]);

        toast({
          title: "Success",
          description: `${uploadedFileNames.length} file(s) uploaded successfully`,
        });
      }
    }
  }
  const handleRemoveImage = (m, j, type) => {
    const t = task;
    const text = t.map((item, i) => {
      if (i === index) {
        return item.map((ite) => {
          if (ite.type === type) {
            return {
              ...ite,
              option: ite.option.map((con, c) => {
                if (c === j) {
                  const newFiles = [...con.files];
                  newFiles.splice(m, 1);
                  return {
                    ...con,
                    files: newFiles
                  };
                }
                return con;
              })
            };
          }
          return ite;
        });
      }
      return item;
    });

    setTask(text);
    setItem(text[index]);
  }

  const addConsequence = (type) => {
    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === type) {
            ite.option.push({ value: "", files: [], current_type: '', method: '' })
          }
        })
      }
      return item
    }
    )
    setTask(text)
    setItem(text[index])
  }

  const addAdditionalControl = () => {
    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === 'responsibility') {
            ite.option.push({ current_type: '', person: '', date: null, value: '', files: [] })
          }
        })
      }
      return item
    }
    )
    setTask(text)
    setItem(text[index])
  }
  const onDeleteConseq = (j, type) => {
    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item.map((ite) => {
          if (ite.type === type) {

            const newHazards = [...ite.option];
            newHazards.splice(j, 1);
            ite.option = newHazards;

          }
        })
      }
      return item
    }
    )
    setTask(text)
    setItem(text[index])
  }


  const handleActivityImage = async (files) => {
    if (files.length > 0) {
      const latestFile = files[files.length - 1];
      const formData1 = new FormData();
      formData1.append('file', latestFile);

      try {
        const response = await apiService.post(FILE_URL, formData1, {
          headers: {
            'Content-Type': 'multipart/form-data',
          }
        });

        if (response) {

          const t = task;
          const text = t.map((item, i) => {
            if (i === index) {

              item[0].images.push(response.files[0].originalname)
            }
            return item
          })
          setTask(text)
          setItem(text[index])

        }
      } catch (error) {
        // Log the error response for debugging purposes
        console.error("File upload error: ", error);
      }

    }
  }
  const changeActivityName = (e) => {

    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item[0].name = e
      }
      return item
    }
    )
    setTask(text)
    setItem(text[index])


  }
  const dataURItoFile = (dataURI: string, filename: string): File => {
    const byteString = atob(dataURI.split(",")[1]);
    // separate out the mime component
    const mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
    // write the bytes of the string to an ArrayBuffer
    const ab = new ArrayBuffer(byteString.length);
    const dw = new DataView(ab);
    for (let i = 0; i < byteString.length; i++) {
      dw.setUint8(i, byteString.charCodeAt(i));
    }

    // write the ArrayBuffer to a blob, and you're done
    return new File([ab], filename, { type: mimeString });
  };

  const editUserHandler = async () => {
    setIsLoading(true);

    let uploadedSignature = '';

    try {
      // If the signature is not empty, upload it

      if (data.teamLeaderDeclaration.sign) {
        uploadedSignature = data.teamLeaderDeclaration.sign
      } else {
        if (!signRef.current.isEmpty()) {
          uploadedSignature = await uploadSignature();
        }
      }

      // Proceed with the patch request, including the signature if available
      const response = await apiService.patch(RISK_WITH_ID_URL(data?.id || ''), {
        type: type === 'routine' ? 'Routine' : type === 'nonroutine' ? 'Non Routine' : 'High-Risk Hazard',
        tasks: task,
        shortName: shortName,
        workActivityId: type === 'nonroutine' ? "" : selectedActivity?.value || "",
        departmentId: type === 'nonroutine' ? "" : selectedDepart?.value || "",
        teamLeaderDeclaration: { name: user?.firstName, sign: uploadedSignature || '' },
        overallRecommendationOne: recommendationOne,
        overallRecommendationTwo: recommendationTwo,
        additonalRemarks: additionalRecommendation,
        highRisk: eptwHot,
        nonRoutineDepartment: nonRoutineDepartment,
        nonRoutineWorkActivity: nonRoutineActivity,
        status: 'Pending',
        hazardName: hazardName,
        raTeamMembersList: selectedCrew,
        description: activityDesc,
      });

      setIsLoading(false);
      toast({
        title: "Success",
        description: "Risk Assessment Updated!",
      });
      navigate('/risk-assessment');
    } catch (error) {
      console.error("Error updating risk assessment: ", error);
      setIsLoading(false);
      toast({
        title: "Error",
        description: "Failed to update risk assessment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const createUserHandler = async () => {
    setIsLoading(true);

    // Check if the signature is empty
    if (!signRef.current || signRef.current.isEmpty()) {
      toast({
        title: "Error",
        description: "Please provide your signature",
        variant: "destructive",
      });
      setIsLoading(false);
      return;
    }

    let uploadedSignature = '';

    // If the signature is not empty, upload the signature
    const filename = new Date().getTime() + "captin_sign.png";
    const formData = new FormData();
    formData.append('file', dataURItoFile(signRef.current.toDataURL("image/png"), filename));

    try {
      const response = await apiService.post(FILE_URL, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });

      if (response?.files?.[0]?.originalname) {
        uploadedSignature = response.files[0].originalname;
      } else {
        throw new Error("File upload failed.");
      }
    } catch (error) {
      console.error("File upload error: ", error);
      setIsLoading(false);
      toast({
        title: "Error",
        description: "Failed to upload signature. Please try again.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Proceed with the API to create the risk assessment
      const response = await apiService.post(RISKASSESSMENT_LIST, {
        type: type === 'routine' ? 'Routine' : type === 'nonroutine' ? 'Non Routine' : 'High-Risk Hazard',
        tasks: task,
        teamLeaderDeclaration: { name: user?.firstName, sign: uploadedSignature },
        workActivityId: type === 'nonroutine' ? "" : selectedActivity?.value || "",
        departmentId: type === 'nonroutine' ? "" : selectedDepart?.value || "",
        teamLeaderId: user?.id,
        overallRecommendationOne: recommendationOne,
        overallRecommendationTwo: recommendationTwo,
        additonalRemarks: additionalRecommendation,
        highRisk: eptwHot,
        nonRoutineDepartment: nonRoutineDepartment,
        nonRoutineWorkActivity: nonRoutineActivity,
        status: 'Pending',
        hazardName: hazardName,
        raTeamMembersList: selectedCrew,
        description: activityDesc,
        shortName: shortName
      });

      setIsLoading(false);
      toast({
        title: "Success",
        description: "Risk Assessment Created!",
      });
      navigate('/risk-assessment');
    } catch (error) {
      console.error("Risk assessment creation error: ", error);
      setIsLoading(false);
      toast({
        title: "Error",
        description: "Failed to create risk assessment. Please try again.",
        variant: "destructive",
      });
    }
  };


  const uploadSignature = async () => {
    if (!signRef.current) throw new Error("Signature canvas not available");

    const filename = new Date().getTime() + "captin_sign.png";
    const formData = new FormData();
    formData.append('file', dataURItoFile(signRef.current.toDataURL("image/png"), filename));

    try {
      const response = await apiService.post(FILE_URL, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });

      if (response?.files?.[0]?.originalname) {
        return response.files[0].originalname;
      } else {
        throw new Error("File upload failed.");
      }
    } catch (error) {
      console.error("File upload error: ", error);
      throw error;
    }
  };

  const saveDraftRiskAssessment = async (sign) => {
    try {
      const response1 = await apiService.post(DRAFT_RA, {
        type: type === 'routine' ? 'Routine' : type === 'nonroutine' ? 'Non Routine' : 'High-Risk Hazard',
        tasks: task,
        teamLeaderDeclaration: { name: user?.firstName || 'Team Leader', sign: sign || '' }, // Use empty string if sign is not available
        workActivityId: type === 'nonroutine' ? "" : selectedActivity?.value || "",
        departmentId: type === 'nonroutine' ? "" : selectedDepart?.value || "",
        teamLeaderId: user?.id || "",
        overallRecommendationOne: recommendationOne || {},
        overallRecommendationTwo: recommendationTwo || {},
        additonalRemarks: additionalRecommendation,
        highRisk: eptwHot,
        nonRoutineDepartment: nonRoutineDepartment,
        nonRoutineWorkActivity: nonRoutineActivity,
        status: 'Draft',
        hazardName: hazardName,
        raTeamMembersList: selectedCrew,
        description: activityDesc
      });

      console.log("Draft save response:", response1);

      // If we reach here without an exception, the request was successful
      return true;
    } catch (error) {
      console.error("Risk assessment save error: ", error);
      throw error; // Rethrow to handle it in the main function
    }
  };

  const draftUserHandler = async () => {
    setIsLoading(true);

    try {
      let uploadedSignature = '';

      // Check if the signature is not empty, if it's not, upload the signature
      if (signRef.current && !signRef.current.isEmpty()) {
        uploadedSignature = await uploadSignature();
      }

      // Run the second API even if the signature is empty
      const saveDraft = await saveDraftRiskAssessment(uploadedSignature);

      if (saveDraft) {
        setIsLoading(false);
        toast({
          title: "Success",
          description: "Risk Assessment Drafted!",
        });
        navigate('/risk-assessment');
      }
    } catch (error) {
      console.error("Draft save error:", error);
      toast({
        title: "Error",
        description: "Please try again!",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };
  const draftUserEditHandler = async () => {
    setIsLoading(true);

    try {
      let uploadedSignature = '';

      // If the signature is not empty, upload the signature first
      if (data.teamLeaderDeclaration.sign) {
        uploadedSignature = data.teamLeaderDeclaration.sign
      } else {
        if (signRef.current && !signRef.current.isEmpty()) {
          uploadedSignature = await uploadSignature();
        }
      }


      // Now patch the risk assessment draft with the signature if available
      const response1 = await apiService.patch(RISK_UPDATE_DRAFT_WITH_ID(data?.id || ''), {
        type: type === 'routine' ? 'Routine' : type === 'nonroutine' ? 'Non Routine' : 'High-Risk Hazard',
        tasks: task,
        teamLeaderDeclaration: { name: user?.firstName || 'Team Leader', sign: uploadedSignature || '' }, // Pass the signature if available
        workActivityId: type === 'nonroutine' ? "" : selectedActivity?.value || "",
        departmentId: type === 'nonroutine' ? "" : selectedDepart?.value || "",
        overallRecommendationOne: recommendationOne,
        overallRecommendationTwo: recommendationTwo,
        additonalRemarks: additionalRecommendation,
        highRisk: eptwHot,
        nonRoutineDepartment: nonRoutineDepartment,
        nonRoutineWorkActivity: nonRoutineActivity,
        status: 'Draft',
        hazardName: hazardName,
        raTeamMembersList: selectedCrew,
        description: activityDesc
      });

      // If we reach here without an exception, the request was successful
      setIsLoading(false);
      toast({
        title: "Success",
        description: "Risk Assessment Drafted!",
      });
      navigate('/risk-assessment');
    } catch (error) {
      console.error("Error updating risk assessment: ", error);
      toast({
        title: "Error",
        description: "Please try again!",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  const checkHotWork = (e, item) => {

    if (e.target.checked) {
      setEptwHot((prev) => [...prev, item]);

    } else {

      setEptwHot(prevData => prevData.filter(item1 => item1.id !== item.id));
    }


  }

  const editSubActivityTitle = (activity, file) => {
    console.log(activity, file)

    const t = task
    const text = t.map((item, i) => {
      if (i === index) {
        item[0].name = activity
        item[0].images = file
      }
      return item
    }
    )
    setTask(text)
    setItem(text[index])

  }

  const getStatusClass = (status) => {
    if (status === 'completed') return 'completed-stage';
    if (status === 'inprogress') return 'incomplete-stage';
    return 'not-started-stage';
  };

  const sendNotification = async () => {
    let check = true
    if (!selectedActivity) {
      check = false
    } else if (!selectedDepart) {
      check = false
    } else if (selectedCrew.length === 0) {
      check = false
    }
    if (check) {
      const response = await apiService.post(SENT_NOTIFICATION_MAIL, {

        activity: selectedActivity,
        depart: selectedDepart,
        member: selectedCrew,
        leader: user?.firstName || 'Team Leader',
      })
      console.log(response)
      // If we reach here without an exception, the request was successful
      toast({
        title: "Success",
        description: "Notification Sent!",
      });
    } else {
      toast({
        title: "Warning",
        description: "Please Select Activity, Department, and Team Members!",
        variant: "destructive",
      });
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <Card>
          <CardContent className="space-y-6">
            <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 mt-6 mb-6">
              {type === 'routine' ?
                <>
                  <p className='fst-italic'>A risk evaluation conducted for activities that are regularly performed and require controls in place within the organization’s operations.</p>
                  <p className=''>To proceed, select the relevant Department and Work Activity from the drop-down list of pre-configured options. If you cannot find a specific department or work activity, check the Risk Register, as a risk assessment may have already been completed for that activity. If it is not listed, please contact the Enterprise Administrator to have it added.</p>
                  <p className=''>If the work activity is not routinely conducted by the organization and is therefore not found in the work activity register, switch to Non-Routine Risk Assessment and manually enter the title of the work to continue.</p>
                </>
                :
                <>
                  <p className='fst-italic '>An evaluation focused on activities that are not regularly performed and do not have established controls, requiring a detailed assessment to ensure appropriate safeguards are identified and implemented.</p>
                </>
              }
            </div>

            {/* Tab Structure */}
            <Tabs value={currentTab} onValueChange={setCurrentTab} className="w-full">
              <TabsList className={`grid w-full ${domain === 'edit' ? 'grid-cols-4' : 'grid-cols-3'}`}>
                <TabsTrigger value="general">General Information</TabsTrigger>
                <TabsTrigger value="detailed">Detailed Assessment</TabsTrigger>
                <TabsTrigger value="recommendation">Recommendation</TabsTrigger>
                {domain === 'edit' && (
                  <TabsTrigger value="changelog">Change Log</TabsTrigger>
                )}
              </TabsList>

              {/* Tab 1: General Information */}
              <TabsContent value="general" className="space-y-6 mt-6">
                {/* Activity Configuration Section */}
                <Card className="mb-8 shadow-md border-0">
              <CardHeader className="bg-gray-50 border-b">
                <CardTitle className="text-xl font-semibold text-gray-800 flex items-center gap-2">
                  <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Building className="w-4 h-4 text-blue-600" />
                  </div>
                  Activity Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                {type === 'routine' ? (
                  <div className="space-y-8">
                    <div className="flex flex-col md:flex-row gap-8">
                      <div className="flex-1 space-y-3 flex flex-col">
                        <Label htmlFor="department" className="text-sm font-semibold text-gray-700">
                          Choose Operational Risk Area
                        </Label>
                        <p className="text-xs text-gray-500 mb-2">
                          Select the department or operational area where this activity will be performed.
                        </p>
                        <div className="flex-1 flex items-end">
                          <Select
                            value={selectedDepart?.value || ''}
                            onValueChange={(value) => {
                              const dept = depart.find(d => d.value === value);
                              setSelectedDepart(dept || null);
                            }}
                          >
                            <SelectTrigger className="h-11 w-full">
                              <SelectValue placeholder="Select Operational Risk Area" />
                            </SelectTrigger>
                            <SelectContent>
                              {depart.map((dept) => (
                                <SelectItem key={dept.value} value={dept.value}>
                                  {dept.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="flex-[2] space-y-3 flex flex-col">
                        <Label htmlFor="activity" className="text-sm font-semibold text-gray-700">
                          Choose Work Activity
                        </Label>
                        <p className="text-xs text-gray-500 mb-2">
                          Select the specific work activity to be performed in the selected operational area.
                        </p>
                        <div className="flex-1 flex items-end">
                          <Select
                            value={selectedActivity?.value || ''}
                            onValueChange={(value) => {
                              const act = activity.find(a => a.value === value);
                              setSelectedActivity(act || null);
                            }}
                          >
                            <SelectTrigger className="h-11 w-full">
                              <SelectValue placeholder="Select Work Activity" />
                            </SelectTrigger>
                            <SelectContent>
                              {activity.map((act) => (
                                <SelectItem key={act.value} value={act.value}>
                                  {act.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : type === 'nonroutine' ? (
                  <div className="space-y-8">
                    <div className="max-w-2xl space-y-4">
                      <div className="space-y-3">
                        <Label htmlFor="nonroutine-activity" className="text-sm font-semibold text-gray-700">
                          Work Activity Title
                        </Label>
                        <p className="text-sm text-gray-600 leading-relaxed">
                          Enter a clear and descriptive title for the non-routine work activity.
                          This should include the specific task, location, and any relevant details that help identify the scope of work.
                        </p>
                        <Textarea
                          id="nonroutine-activity"
                          placeholder="Enter work activity title (e.g., 'Emergency repair of cooling system in Building A')"
                          value={nonRoutineActivity}
                          onChange={(e) => setNonRoutineActivity(e.target.value)}
                          className="min-h-[100px] resize-none"
                          rows={4}
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-8">
                    <div className="max-w-xl space-y-4">
                      <div className="space-y-3">
                        <Label htmlFor="hazard-name" className="text-sm font-semibold text-gray-700">
                          Hazard Name
                        </Label>
                        <p className="text-sm text-gray-600 leading-relaxed">
                          Specify the primary hazard or high-risk scenario that requires assessment.
                          Use clear, specific terminology that accurately describes the potential danger.
                        </p>
                        <Input
                          id="hazard-name"
                          placeholder="Enter hazard name (e.g., 'Working at heights above 2m')"
                          value={hazardName}
                          onChange={(e) => setHazardName(e.target.value)}
                          className="h-11"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Team Members Section */}
                <div className="mt-12 pt-8 border-t border-gray-200">
                  <div className="space-y-6">
                    <div>
                      <Label htmlFor="team-members" className="text-sm font-medium">
                        Identify the qualified RA Team Members to include in this Risk Assessment using the drop-down selector; only qualified members will be listed. If a required member is not listed, please contact the Administrator to have them added. Once you’ve made your selections, click the Send Notification button to notify them via email about their inclusion in the team.
                      </Label>

                      <MultiSelect
                        options={crew.map(member => ({
                          label: member.name,
                          value: member.id
                        }))}
                        selected={selectedCrew
                          .filter(selected => crew.find(c => c.id === selected.id))
                          .map(member => member.id)
                        }
                        onChange={(values) => {
                          // Only allow selection from current crew members
                          const selectedMembers = crew.filter(member => values.includes(member.id));
                          setSelectedCrew(selectedMembers);
                        }}
                        placeholder="Choose Members..."
                        className="mt-2"
                      />

                    </div>

                    <div>
                      <Button
                        variant="outline"
                        onClick={() => sendNotification()}
                        className="w-fit"
                      >
                        <Send className="w-4 h-4 mr-2" />
                        Send Notification
                      </Button>
                    </div>
                  </div>
                </div>

              </CardContent>
            </Card>

            {/* Activity Description Section */}
            <Card className="mb-8 shadow-md border-0">
              <CardHeader className="bg-gray-50 border-b">
                <CardTitle className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                  <div className="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center">
                    <FileText className="w-4 h-4 text-green-600" />
                  </div>
                  Activity Description
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div>
                  <Label htmlFor="activity-description" className="text-sm font-medium text-gray-700 mb-2 block">
                    Provide additional information about the work activity to clarify scope (Optional)
                  </Label>
                  <Textarea
                    id="activity-description"
                    rows={4}
                    value={activityDesc}
                    onChange={(e) => setActivityDesc(e.target.value)}
                    className="mt-1 resize-none"
                    placeholder="Enter detailed description of the work activity, including scope, objectives, and any specific requirements..."
                  />
                </div>
              </CardContent>
            </Card>
              </TabsContent>

              {/* Tab 2: Detailed Assessment */}
              <TabsContent value="detailed" className="space-y-6 mt-6">
                {/* Sub Activities Section */}
                <Card className="mb-8 shadow-md border-0">
              <CardHeader className="bg-gray-50 border-b">
                <CardTitle className="text-xl font-semibold text-gray-800 flex items-center gap-2">
                  <div className="w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center">
                    <ClipboardList className="w-4 h-4 text-purple-600" />
                  </div>
                  Sub Activities
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="mb-6">
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div className="space-y-3 text-sm text-gray-700">
                      <p className="font-medium text-purple-800">
                        📋 Sub-Activity Management
                      </p>
                      <p>
                        Identify and list all sub-activities associated with the selected process. For each sub-activity, provide a clear description and, where feasible, upload any relevant images. These images will be utilized in the risk communication modules to enhance understanding and awareness of the process and associated risks.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <p className="text-sm text-gray-600">
                    Click on each listed sub-activity and follow the provided guidance to identify the hazards, potential consequences, current controls, and assess the risks specific to that sub-activity.
                  </p>
                </div>

                <div className="mb-6">
                  {task.length === 0 ? (
                    <p className="text-muted-foreground">No sub-activities added</p>
                  ) : (
                    <div className="space-y-3">
                      {task.map((item, i) => (
                        <TaskItemComponent
                          key={i}
                          item={item}
                          index={i}
                          openDialog={openDialog}
                          subActivity={subActivity}
                          deleteTask={deleteTask}
                          onDragStart={handleDragStart}
                          onDrop={handleDrop}
                          onDragOver={handleDragOver}
                          cellClassName={cellClassName}
                          tableData={tableData}
                        />
                      ))}
                    </div>
                  )}
                </div>

                  {addSubActivity && (
                  <Card className="mt-6 border-2 border-dashed border-purple-300 bg-purple-50/50">
                    <CardHeader className="bg-purple-100 border-b border-purple-200">
                      <CardTitle className="text-lg font-semibold text-purple-800 flex items-center gap-2">
                        <Plus className="w-5 h-5" />
                        New Sub-Activity
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-6">
                      <div className="space-y-6">
                        <div>
                          <Label htmlFor="sub-activity-name" className="text-sm font-medium text-gray-700 mb-2 block">
                            Sub-Activity Name *
                          </Label>
                          <Input
                            id="sub-activity-name"
                            value={subActivityName}
                            onChange={(e) => setSubActivityName(e.target.value)}
                            className="mt-1"
                            placeholder="Enter a descriptive name for this sub-activity..."
                          />
                        </div>

                        <div>
                          <Label htmlFor="image-uploads" className="text-sm font-medium text-gray-700 mb-2 block">
                            Image Uploads (Optional)
                          </Label>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 bg-gray-50">
                            <MultiFileUpload
                              onFilesChange={(uploadedFiles: File[]) => setFiles(uploadedFiles)}
                              acceptedTypes={['image/jpeg', 'image/png', 'image/jpg']}
                              maxFiles={5}
                              maxFileSize={104857600}
                              description="Upload images to help illustrate this sub-activity (JPEG, PNG - Max 5 files)"
                            />
                          </div>
                        </div>

                        <div className="flex gap-3 pt-4 border-t border-gray-200">
                          <Button
                            onClick={() => AddSubActivityTitle()}
                            className="bg-green-600 hover:bg-green-700 text-white"
                          >
                            <Save className="w-4 h-4 mr-2" />
                            Save Sub-Activity
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => setAddSubActivity(false)}
                            className="border-gray-300 text-gray-700 hover:bg-gray-50"
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                <div className="flex justify-center">
                  <Button
                    onClick={() => setAddSubActivity(!addSubActivity)}
                    className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 mt-4 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg"
                    size="lg"
                  >
                    <Plus className="w-5 h-5 mr-2 " />
                    Add Sub-Activity
                  </Button>
                </div>

              
              </CardContent>
            </Card>
              </TabsContent>

              {/* Tab 3: Recommendation */}
              <TabsContent value="recommendation" className="space-y-6 mt-6">
                {/* Overall Recommendations Section */}
                <Card className="mb-8 shadow-md border-0">
              <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-700 text-white">
                <CardTitle className="text-xl font-semibold flex items-center gap-2">
                  <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                    <AlertTriangle className="w-5 h-5" />
                  </div>
                  Overall Recommendations of the RA Team
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">

                <div className="space-y-8">
                  {/* 1. First recommendation */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg shadow-md">
                        1
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-800 mb-3">Overall Risk Level Assessment</h3>
                        <Select
                          value={recommendationOne?.value || ''}
                          onValueChange={(value) => {
                            const option = [
                              { label: 'The overall risk level for this work activity is LOW. No further additional controls actions are necessary. Monitoring is required to ensure that the controls are maintained.', value: '0' },
                              { label: 'The overall risk level for this work activity is MEDIUM. Work can progress with close supervision and monitoring of current controls. Additional controls identified should be implemented within the defined period of time.', value: '1' },
                              { label: 'The overall risk level for this work activity is HIGH. Work should not be started or continued until the risk level has been reduced and risk numbers enters the LOW or MEDIUM zone.', value: '2' }
                            ].find(opt => opt.value === value);
                            setRecommendationOne(option || null);
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Choose overall risk level..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="0">The overall risk level for this work activity is LOW. No further additional controls actions are necessary. Monitoring is required to ensure that the controls are maintained.</SelectItem>
                            <SelectItem value="1">The overall risk level for this work activity is MEDIUM. Work can progress with close supervision and monitoring of current controls. Additional controls identified should be implemented within the defined period of time.</SelectItem>
                            <SelectItem value="2">The overall risk level for this work activity is HIGH. Work should not be started or continued until the risk level has been reduced and risk numbers enters the LOW or MEDIUM zone.</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  {/* 2. Second recommendation */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-full flex items-center justify-center font-bold text-lg shadow-md">
                        2
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-800 mb-3">Permit Requirements</h3>
                        <Select
                          value={recommendationTwo?.value || ''}
                          onValueChange={(value) => {
                            const option = [
                              { label: 'Since this is routine activity with low risk, no formal permit is recommended. However, a dynamic review is advised in case of changing circumstances.', value: '0' },
                              { label: 'A formal permit to work is required before work can commence. Duration will be specified by the permit approver.', value: '1' },
                              { label: 'A formal permit to work that is required. This is valid for a specific task and limited duration. The permit shall include the names of individuals involved in the work.', value: '2' }
                            ].find(opt => opt.value === value);
                            setRecommendationTwo(option || null);
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Choose permit requirement..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="0">Since this is routine activity with low risk, no formal permit is recommended. However, a dynamic review is advised in case of changing circumstances.</SelectItem>
                            <SelectItem value="1">A formal permit to work is required before work can commence. Duration will be specified by the permit approver.</SelectItem>
                            <SelectItem value="2">A formal permit to work that is required. This is valid for a specific task and limited duration. The permit shall include the names of individuals involved in the work.</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  {/* 3. High-risk permits */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-full flex items-center justify-center font-bold text-lg shadow-md">
                        3
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-800 mb-3">High-Risk Permits</h3>
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                          <p className="mb-4 text-sm">
                            Considering the hazards and risks associated with this work activity, the RA team requires the following high-risk permits to be approved and active when applying for a permit for this specific activity:
                          </p>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {risk.length !== 0 &&
                              risk.map((item) => (
                                <div key={item.id} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`risk-${item.id}`}
                                    checked={eptwHot && eptwHot.some(item1 => item1.id === item.id)}
                                    onCheckedChange={(checked) => {
                                      const event = { target: { value: item.hazardName, checked } };
                                      checkHotWork(event, item);
                                    }}
                                  />
                                  <Label htmlFor={`risk-${item.id}`} className="text-sm">
                                    {item.hazardName}
                                  </Label>
                                </div>
                              ))
                            }
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 4. Additional recommendation */}
                  <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full flex items-center justify-center font-bold text-lg shadow-md">
                        4
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-800 mb-3">Additional Recommendations</h3>
                        <Label htmlFor="additional-recommendation" className="text-sm font-medium text-gray-700 mb-2 block">
                          Provide any additional recommendations or considerations
                        </Label>
                        <Textarea
                          id="additional-recommendation"
                          rows={4}
                          value={additionalRecommendation}
                          onChange={(e) => setAdditionalRecommendation(e.target.value)}
                          className="mt-1 resize-none"
                          placeholder="Enter any additional recommendations, special considerations, or notes..."
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {domain === 'edit' && (
              <div className="bg-white border border-gray-200 rounded-lg shadow-md overflow-hidden mb-6">
                {/* Header Section */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200 p-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    <div>
                      <h6 className="text-xl font-bold text-gray-800 mb-1">Team Members Declaration</h6>
                      <p className="text-sm text-blue-600">Collaborative Risk Assessment Acknowledgment</p>
                    </div>
                  </div>
                </div>

                {/* Declaration Text */}
                <div className="p-6 bg-blue-50 border-b border-blue-100">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <p className="text-blue-800 leading-relaxed">
                      <strong>Declaration:</strong> As a member of the team for this exercise, I confirm that I have actively contributed to identifying the potential consequences of this Critical High Risk Activity and in determining the necessary controls. These controls reflect our collective professional judgment and are essential for ensuring safety. The conclusions we reached were the result of collaboration and consensus, utilizing our team’s full range of expertise.
                    </p>
                  </div>
                </div>

                {/* Team Members Table */}
                <div className="p-6">
                  <div className="overflow-hidden rounded-lg border border-gray-200">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Team Member
                          </th>
                          <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Signature & Date of Affirmation
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {raTeamMember.length !== 0 && raTeamMember.map((item, index) => (
                          <tr key={index} className="hover:bg-gray-50 transition-colors">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                  <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                  </svg>
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900">
                                    {item.user?.firstName || 'N/A'} {item.user?.lastName || ''}
                                  </div>
                                  <div className="text-sm text-gray-500">Team Member</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              {item.signature ? (
                                <div className="flex flex-col space-y-3">
                                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-2 w-32 h-20 flex items-center justify-center overflow-hidden">
                                    <div className="max-w-full max-h-full">
                                      <ImageComponent fileName={item.signature} size={'80'} name={false} />
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                                      <svg className="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                      </svg>
                                    </div>
                                    <span className="text-sm font-medium text-green-700">
                                      Signed on {format(new Date(item.signatureDate), 'dd-MM-yyyy')}
                                    </span>
                                  </div>
                                </div>
                              ) : (
                                <div className="flex items-center gap-2">
                                  <div className="w-5 h-5 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <svg className="w-3 h-3 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                  </div>
                                  <span className="text-sm font-medium text-yellow-700">Pending Signature</span>
                                </div>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}


            <div className="bg-white border border-gray-200 rounded-lg shadow-md overflow-hidden">
              {/* Header Section */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-200 p-6">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h5 className="text-xl font-bold text-gray-800 mb-1">Team Leader Declaration</h5>
                    <p className="text-sm text-green-600">Risk Assessment Leadership Acknowledgment</p>
                  </div>
                </div>
              </div>

              {/* Declaration Text */}
              <div className="p-6 bg-green-50 border-b border-green-100">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="text-green-800 leading-relaxed">
                    <strong>Leadership Declaration:</strong>



                    {type === 'routine' ?
                      <p>As the Team Leader for this Routine Risk Assessment, I affirm my role in guiding the identification of potential risks and necessary controls. The controls listed have been evaluated by the team based on their professional expertise and are essential for ensuring safety in this activity. This outcome reflects the collective judgment of the team, reached through collaboration and consensus.
                      </p> : type === 'nonroutine' ?
                        <p>As the Team Leader for this Non-Routine Risk Assessment, I confirm my responsibility in guiding the team through the identification of potential risks and necessary controls for activities that are not part of the organization’s routine work activity inventory. Given that these activities are not regularly undertaken, we are placing additional focus on documenting the risks and ensuring that appropriate controls are in place. The controls identified are essential for maintaining safety, and this conclusion has been reached through consensus based on the team’s collective professional judgment and experience.
                        </p> : ''
                    }

                  </div>
                </div>
              </div>

              {/* Signature Section */}
              <div className="p-6">
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-8 border border-gray-200">
                  <div className="text-center mb-6">
                    <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">Team Leader Signature</h3>
                    <p className="text-sm text-gray-600">Digital signature confirms leadership accountability</p>
                  </div>

                  {domain === 'edit' ? (
                    data && data.teamLeaderDeclaration && data.teamLeaderDeclaration?.sign ? (
                      <div className="flex flex-col items-center space-y-4">
                        <div className="bg-white border-2 border-green-200 rounded-xl p-4 w-100 shadow-lg">
                          <ImageComponent fileName={data.teamLeaderDeclaration.sign} size={'100'} name={false} />
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-2 mb-2">
                            <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <span className="text-sm font-medium text-green-700">Digitally Signed</span>
                          </div>
                          <p className="text-lg font-semibold text-gray-800">
                            {data.teamLeaderDeclaration.name}
                          </p>
                          <p className="text-sm text-gray-500">Team Leader</p>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center space-y-4">
                        <div className="relative bg-white rounded-xl border-2 border-dashed border-gray-300 hover:border-green-400 transition-colors">
                          <SignatureCanvas
                            penColor="#059669"
                            canvasProps={{
                              width: 450,
                              height: 120,
                              className: "rounded-xl",
                            }}
                            ref={signRef}
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            className="absolute top-2 right-2 bg-white/90 hover:bg-white border-gray-300"
                            onClick={() => signRef.current?.clear()}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                            <p className="text-gray-400 text-sm">Sign here</p>
                          </div>
                        </div>
                        <div className="text-center">
                          <p className="text-lg font-semibold text-gray-800">
                            {user?.firstName || 'Team Leader'}
                          </p>
                          <p className="text-sm text-gray-500">Team Leader</p>
                        </div>
                      </div>
                    )
                  ) : (
                    <div className="flex flex-col items-center space-y-4">
                      <div className="relative bg-white rounded-xl border-2 border-dashed border-gray-300 hover:border-green-400 transition-colors">
                        <SignatureCanvas
                          penColor="#059669"
                          canvasProps={{
                            width: 450,
                            height: 120,
                            className: "rounded-xl",
                          }}
                          ref={signRef}
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          className="absolute top-2 right-2 bg-white/90 hover:bg-white border-gray-300"
                          onClick={() => signRef.current?.clear()}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                          <p className="text-gray-400 text-sm">Sign here</p>
                        </div>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-semibold text-gray-800">
                          {user?.firstName || 'Team Leader'}
                        </p>
                        <p className="text-sm text-gray-500">Team Leader</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-md">
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Button
                  variant="outline"
                  onClick={(e) => {
                    e.preventDefault();
                    if (domain === 'edit') {
                      draftUserEditHandler();
                    } else {
                      draftUserHandler();
                    }
                  }}
                  disabled={isLoading}
                  className="px-8 py-3 border-2 border-gray-300 hover:border-gray-400 text-gray-700 hover:bg-gray-50 transition-all duration-200"
                  size="lg"
                >
                  <Save className="w-5 h-5 mr-2" />
                  Save as Draft
                </Button>
                <Button
                  onClick={(e) => {
                    e.preventDefault();
                    if (domain === 'edit') {
                      if (data?.status === 'Draft') {
                        editUserHandler();
                      } else {
                        setRiskUpdate(true);
                      }
                    } else {
                      createUserHandler();
                    }
                  }}
                  disabled={isLoading}
                  className="px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                  size="lg"
                >
                  <Send className="w-5 h-5 mr-2" />
                  Release Draft for Affirmation
                </Button>
              </div>
            </div>
              </TabsContent>

              {/* Tab 4: Change Log - Only show in edit mode */}
              {domain === 'edit' && (
                <TabsContent value="changelog" className="space-y-6 mt-6">
                  <Card className="mb-8 shadow-md border-0">
                    <CardHeader className="bg-gradient-to-r from-gray-600 to-gray-700 text-white">
                      <CardTitle className="text-xl font-semibold flex items-center gap-2">
                        <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                          <FileText className="w-5 h-5" />
                        </div>
                        Risk Assessment Change Log
                      </CardTitle>
                      <p className="text-gray-100 mt-2">
                        Track all updates and modifications made to this risk assessment over time.
                      </p>
                    </CardHeader>
                    <CardContent className="p-6">
                      {Update.length !== 0 ? (
                        <UpdateTable data={Update} />
                      ) : (
                        <div className="text-center py-8">
                          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <FileText className="w-8 h-8 text-gray-400" />
                          </div>
                          <h3 className="text-lg font-medium text-gray-900 mb-2">No Changes Recorded</h3>
                          <p className="text-gray-500">
                            No updates have been made to this risk assessment yet.
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              )}
            </Tabs>
          </CardContent>
        </Card>

        {riskUpdate && <RiskUpdate show={riskUpdate} onChangeModel={setRiskUpdate} id={data?.id} onSubmitUpdate={onSubmitUpdate} />}

        {isLoading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <Card className="p-6">
              <CardContent className="flex items-center space-x-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <p className="text-sm font-medium">Processing...</p>
              </CardContent>
            </Card>
          </div>
        )}

        {item && (
          <Dialog open={visible} onOpenChange={setVisible}>
            <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto bg-white">
              <DialogHeader className="bg-white border-b border-gray-200 p-6 -m-6 mb-6">
                <DialogTitle className="text-2xl font-bold flex items-center gap-3 text-gray-800">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <ClipboardList className="w-6 h-6 text-blue-600" />
                  </div>
                  Sub-Activity Risk Assessment
                </DialogTitle>
                <p className="text-gray-600 mt-2">
                  Comprehensive risk evaluation for individual sub-activity
                </p>
              </DialogHeader>



              <SubActivityComponent
                item={item}
                onSave={editSubActivityTitle}
              />


              {/* Progress Stepper */}
              <div className="bg-white border border-gray-200 p-6 rounded-lg shadow-sm mb-6">
                {item && item[8] && item[8].step !== undefined && (
                  <HeadStepper
                    activeStage={item[8].step}
                    stages={item[9].level}
                    stageStatus={item[11].value}
                    handleStageClick={handleStageClick}
                    getStatusClass={getStatusClass}
                  />
                )}
              </div>

              {/* Hazard Identification Section */}
              {item[8].step === 0 && (
                <Card className="border border-gray-200 shadow-sm">
                  <CardHeader className="bg-white border-b border-gray-200">
                    <CardTitle className="text-xl font-semibold flex items-center gap-3 text-gray-800">
                      <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <AlertTriangle className="w-6 h-6 text-red-600" />
                      </div>
                      Hazard Identification
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6 bg-white">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                      <div className="space-y-3 text-sm text-gray-700">
                        <p className="font-medium text-blue-800 flex items-center gap-2">
                          <AlertTriangle className="w-4 h-4" />
                          Hazard Identification Process
                        </p>
                        <p>
                          For this sub-activity, identify associated hazards by selecting from the various hazard types displayed. Each type will feature icons representing specific hazards. Once selected, these hazards will automatically appear in other modules, such as Risk Communication, Permit to Work, and Toolbox Talks.
                        </p>
                        <p>
                          If you encounter a specific hazard that is not included in the library, please send an email to <strong><EMAIL></strong> so that it can be promptly added to the hazard library.
                        </p>
                      </div>
                    </div>
                    <HazardAccordion
                      hazards={hazards}
                      activeTabIndex={activeTabIndex}
                      setActiveTabIndex={setActiveTabIndex}
                      selectedHazards={item[1].selected}
                      onClickHazards={onClickHazards}
                      required={required}
                      item={item}
                    />
                    <IdentifiedHazards
                      selectedHazards={item[1].selected}
                      onDeleteHaz={onDeleteHaz}
                    />
                  </CardContent>
                </Card>
              )}

              {/* Consequences Section */}
              {item[8].step === 1 && (
                <Card className="border border-gray-200 shadow-sm">
                  <CardHeader className="bg-white border-b border-gray-200">
                    <CardTitle className="text-xl font-semibold flex items-center gap-3 text-gray-800">
                      <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                        <AlertTriangle className="w-6 h-6 text-orange-600" />
                      </div>
                      Consequences Analysis
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6 bg-white">
                    {/* Identified Hazards Reference Section */}
                    {item[1]?.selected && item[1].selected.length > 0 && (
                      <div className="bg-orange-50 border border-orange-200 rounded-lg mb-6">
                        <div
                          className="flex items-center justify-between p-4 cursor-pointer hover:bg-orange-100 transition-colors"
                          onClick={() => setIsHazardsCollapsed(!isHazardsCollapsed)}
                        >
                          <div className="flex items-center gap-2">
                            <AlertTriangle className="w-4 h-4 text-orange-600" />
                            <span className="font-medium text-orange-800">
                              Identified Hazards (Reference)
                            </span>
                            <span className="text-sm text-orange-600 bg-orange-200 px-2 py-1 rounded-full">
                              {item[1].selected.length}
                            </span>
                          </div>
                          <div className="text-orange-600">
                            {isHazardsCollapsed ? (
                              <ChevronDown className="w-4 h-4" />
                            ) : (
                              <ChevronUp className="w-4 h-4" />
                            )}
                          </div>
                        </div>

                        {!isHazardsCollapsed && (
                          <div className="px-4 pb-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                              {item[1].selected.map((hazard, index) => (
                                <div key={index} className="flex items-center gap-3 p-3 bg-white border border-orange-200 rounded-lg">
                                  {hazard.image && (
                                    <img
                                      src={`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${hazard.image}`}
                                      alt={hazard.name}
                                      className="w-8 h-8 object-contain flex-shrink-0"
                                      onError={(e) => {
                                        e.currentTarget.style.display = 'none';
                                      }}
                                    />
                                  )}
                                  <span className="text-sm font-medium text-gray-800 truncate">
                                    {hazard.name}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                      <div className="space-y-3 text-sm text-gray-700">
                        <p className="font-medium text-blue-800 flex items-center gap-2">
                          <ClipboardList className="w-4 h-4" />
                          Consequence Assessment Guidelines
                        </p>
                        <p>
                          For this sub-activity, list and elaborate on all potential consequences associated with the identified hazards, covering impacts on <strong>Personnel</strong>, <strong>Environment</strong>, <strong>Property/Equipment</strong>, and <strong>Operations</strong>, as applicable. Include all relevant areas that apply to the specific hazards of the sub-activity.
                        </p>
                      </div>
                    </div>
                    {item[2].option.map((con, i) => (
                      <Consequence
                        key={i}
                        con={con}
                        i={i}
                        impactOn={impactOn}
                        onImapactOn={onImapactOn}
                        onConseqText={onConseqText}
                        onDeleteConseq={onDeleteConseq}
                        handleTaskFileChange={handleTaskFileChange}
                        required={required}
                        type={'routine'}
                        handleRemoveImage={handleRemoveImage}
                      />
                    ))}
                    <div className="flex justify-center mt-6">
                      <Button
                        onClick={() => addConsequence('consequence')}
                        className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Add Consequence
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Current Controls Section */}
              {item[8].step === 2 && (
                <Card className="border border-gray-200 shadow-sm">
                  <CardHeader className="bg-white border-b border-gray-200">
                    <CardTitle className="text-xl font-semibold flex items-center gap-3 text-gray-800">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <Users className="w-6 h-6 text-green-600" />
                      </div>
                      Current Controls
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6 bg-white">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                      <div className="space-y-3 text-sm text-gray-700">
                        <p className="font-medium text-blue-800 flex items-center gap-2">
                          <Users className="w-4 h-4" />
                          Control Measures Assessment
                        </p>
                        <p>
                          Identify and describe in detail the current controls in place to manage the hazards and minimize their consequences. Current controls refer to existing safety measures, procedures, and other implemented actions to reduce risks associated with the sub-activity.
                        </p>
                      </div>
                    </div>
                    {item[3].option.map((con, i) => (
                      <CurrentControl
                        key={i}
                        con={con}
                        i={i}
                        control={control}
                        controlType={controlType}
                        onMethodOn={onMethodOn}
                        onOwnerChange={onOwnerChange}
                        onImapactOn={onImapactOn}
                        onConseqText={onConseqText}
                        onDeleteConseq={onDeleteConseq}
                        onConseqRequired={onConseqRequired}
                        handleTaskFileChange={handleTaskFileChange}
                        required={required}
                        type={'routine'}
                        handleRemoveMainImage={handleRemoveImage}
                      />
                    ))}



                    {item[3].option.every(con => con.current_type !== "No Control") && (
                      <div className="flex justify-center mt-6">
                        <Button
                          className='bg-blue-600 hover:bg-blue-700 text-white shadow-sm'
                          onClick={() => addConsequence('current_control')}
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Add Current Control
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Risk Assessment Section */}
              {item[8].step === 3 && (
                <Card className="border border-gray-200 shadow-sm">
                  <CardHeader className="bg-white border-b border-gray-200">
                    <CardTitle className="text-xl font-semibold flex items-center gap-3 text-gray-800">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <AlertTriangle className="w-6 h-6 text-purple-600" />
                      </div>
                      Risk Assessment
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6 bg-white">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                      <div className="space-y-3 text-sm text-gray-700">
                        <p className="font-medium text-blue-800 flex items-center gap-2">
                          <AlertTriangle className="w-4 h-4" />
                          Risk Evaluation Matrix
                        </p>
                        <p>
                          Evaluate the risk level by assessing both the severity of potential consequences and the likelihood of occurrence. Use the risk matrix to determine the overall risk rating.
                        </p>
                      </div>
                    </div>

                    <RiskAssessment
                      item={item}
                      severity={severity}
                      severityData={severityData}
                      required={required}
                      onChangeSeverity={onChangeSeverity}
                      likelyhood={likelyhood}
                      levelData={levelData}
                      onChangeLikelyhood={onChangeLikelyhood}
                      rowClassName={rowClassName}
                      tableData={tableData}
                      cellClassName={cellClassName}
                      cellStyle={cellStyle}
                      onChangeReAss={onChangeReAss}
                    />
                  </CardContent>
                </Card>
              )}

              {/* Proposed Risk Management Section */}
              {item[8].step === 4 && (
                <Card className="border border-gray-200 shadow-sm">
                  <CardHeader className="bg-white border-b border-gray-200">
                    <CardTitle className="text-xl font-semibold flex items-center gap-3 text-gray-800">
                      <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <Users className="w-6 h-6 text-indigo-600" />
                      </div>
                      Proposed Risk Management
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6 bg-white">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                      <div className="space-y-3 text-sm text-gray-700">
                        <p className="font-medium text-blue-800 flex items-center gap-2">
                          <Users className="w-4 h-4" />
                          Additional Control Measures
                        </p>
                        <p>
                          Define additional control measures and assign responsibilities to further reduce risk levels. Specify implementation timelines and responsible persons for each control measure.
                        </p>
                      </div>
                    </div>

                    <ProposedRiskManagement
                      item={item}
                      control={controlAdditional}
                      responsibility={responsibility}
                      severity={severity}
                      severityData={severityData}
                      likelyhood={likelyhood}
                      levelData={levelData}
                      tableData={tableData}
                      severityTable={severityTable}
                      likelyhoodTable={likelyhoodTable}
                      riskTable={riskTable}
                      required={required}
                      onControlAddion={onControlAddion}
                      onControlAddionText={onControlAddionText}
                      onDeleteConseq={onDeleteConseq}
                      onResponsePerson={onResponsePerson}
                      onResponseDate={onResponseDate}
                      addAdditionalControl={addAdditionalControl}
                      onChangeSeverity={onChangeSeverity}
                      onChangeLikelyhood={onChangeLikelyhood}
                      cellClassName={cellClassName}
                      cellStyle={cellStyle}
                      rowClassName={rowClassName}
                    />
                  </CardContent>
                </Card>
              )}

              <DialogFooter>
                {footerTemplate}
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

      </div>
    </div>
  );
};

export default RiskAssessmentForm;