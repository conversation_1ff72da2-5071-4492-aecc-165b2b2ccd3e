import React, { useEffect, useState, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { format, isValid } from 'date-fns';
import { fetchAllUsers, User, InspectionResponse } from '@/services/api';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import ImageComponent from '@/components/common/ImageComponent';
import apiService from '@/services/apiService';
import { useReactToPrint } from 'react-to-print';
import {
  Calendar,
  User as UserIcon,
  MapPin,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  Building,
  PenTool,
  TextCursor,
  Heading1,
  Heading2,
  Type,
  ClipboardList,
  Printer,
  Paperclip
} from 'lucide-react';

interface InspectionDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  inspection: InspectionResponse | null;
}

const InspectionDetailsModal: React.FC<InspectionDetailsModalProps> = ({
  open,
  onOpenChange,
  inspection
}) => {
  const [users, setUsers] = useState<User[]>([]);
  const [logo, setLogo] = useState<string>('');
  const { accessToken } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();
  const printRef = useRef<HTMLDivElement>(null);

  const handlePrint = useReactToPrint({
    contentRef: printRef,
    documentTitle: `Inspection_${inspection?.maskId}_${format(new Date(), 'yyyyMMdd_HHmm')}`,
    onAfterPrint: () => {
      toast({
        title: "Print Successful",
        description: "The inspection report has been sent to the printer.",
        variant: "default",
      });
    },
    onPrintError: (error) => {
      console.error('Print error:', error);
      toast({
        title: "Print Failed",
        description: "An error occurred while printing. Please try again.",
        variant: "destructive",
      });
    },
  });

  const getAllUsers = async () => {
    try {
      if (accessToken) {
        const response = await fetchAllUsers(accessToken);
        setUsers(response);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const getFetchLogo = async () => {
    try {
      const logoName = localStorage.getItem('logo');
      if (logoName) {
        const logoUrl = await apiService.get(`/files/presigned-url/${logoName}`);
        setLogo(logoUrl);
      }
    } catch (error) {
      console.error('Error fetching logo:', error);
    }
  };

  useEffect(() => {
    if (open && accessToken) {
      getAllUsers();
      getFetchLogo();
    }
  }, [open, accessToken]);



  const getName = (id: string) => {
    const user = users.find(user => user.id === id);
    return user?.firstName || '';
  };

  // Function to get action status badge (same as ActionLogModal)
  const getActionStatusBadge = (action: any) => {
    let status = '';
    let variant: 'default' | 'secondary' | 'destructive' | 'outline' = 'default';

    switch (action.lastActionType) {
      case 'perform_task':
        status = action.lastStatus === 'Completed' ? 'Assigned' : 'Pending Action';
        variant = 'outline';
        break;
      case 'reperform_task':
        status = 'Re-Assigned';
        variant = 'secondary';
        break;
      case 'verify_task':
        if (action.lastStatus === 'Completed') {
          status = 'Verified & Closed';
          variant = 'default';
        } else {
          status = 'Implemented - Pending Verification';
          variant = 'outline';
        }
        break;
      case 'approve':
        status = 'Verified & Closed';
        variant = 'default';
        break;
      default:
        status = 'Unknown Status';
        variant = 'secondary';
        break;
    }

    return <Badge variant={variant}>{status}</Badge>;
  };

  // Function to group actions by description (same as InspectionPage)
  const groupByDescription = (data: any[]) => {
    const filterData = data.filter(item =>
      item.actionType !== 'review_incident' &&
      item.actionType !== 'conduct_inspection' &&
      item.actionType !== 'approve_investigation'
    );

    const groupedData = [];
    const descriptionMap: any = {};

    filterData.forEach(item => {
      const { objectId, actionType, assignedToId, status, trackId } = item;
      if (!descriptionMap[trackId]) {
        descriptionMap[trackId] = {
          objectId: objectId,
          firstActionType: actionType,
          lastActionType: actionType,
          actionTypes: [actionType],
          lastAssignedToId: assignedToId,
          lastStatus: status,
          data: []
        };
      } else {
        descriptionMap[trackId].lastActionType = actionType;
        descriptionMap[trackId].actionTypes.push(actionType);
        descriptionMap[trackId].lastAssignedToId = assignedToId;
        descriptionMap[trackId].lastStatus = status;
      }
      descriptionMap[trackId].data.push(item);
    });

    // Update lastActionType, lastAssignedToId, and lastStatus in each group
    for (const description in descriptionMap) {
      const group = descriptionMap[description];
      const lastDataObject = group.data[group.data.length - 1];
      group.lastActionType = lastDataObject.actionType;
      group.lastAssignedToId = lastDataObject.assignedToId;
      group.lastStatus = lastDataObject.status;
      groupedData.push(group);
    }

    return groupedData;
  };



  const formatDate = (dateString: string | undefined | null) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      return isValid(date) ? format(date, 'dd-MM-yyyy') : "N/A";
    } catch {
      return "N/A";
    }
  };

  if (!inspection) return null;

  const locationDisplay = [
    inspection.locationOne,
    inspection.locationTwo,
    inspection.locationThree,
    inspection.locationFour,
    inspection.locationFive,
    inspection.locationSix
  ]
    .filter(location => location?.name)
    .map(location => location.name)
    .join(' > ') || "N/A";

  const getStatusBadge = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return <Badge className="bg-green-500 text-white"><CheckCircle className="w-3 h-3 mr-1" />Completed</Badge>;
      case 'in progress':
        return <Badge className="bg-blue-500 text-white"><Clock className="w-3 h-3 mr-1" />In Progress</Badge>;
      case 'scheduled':
        return <Badge className="bg-yellow-500 text-white"><Calendar className="w-3 h-3 mr-1" />Scheduled</Badge>;
      default:
        return <Badge variant="secondary"><AlertCircle className="w-3 h-3 mr-1" />{status || 'Unknown'}</Badge>;
    }
  };

  const renderChecklistComponents = () => {
    // Handle both new format (components array) and legacy format (direct value object)
    let components: any[] = [];

    // Access the inspection value data - try multiple possible locations
    const inspectionValue = (inspection as any).value ||
      (inspection as any).checklist?.value ||
      (inspection as any).checklistValue ||
      {};

    if (inspectionValue) {
      if (Array.isArray(inspectionValue)) {
        // Legacy format - array of groups
        components = inspectionValue.map((group: any, index: number) => ({
          id: `legacy-${index}`,
          type: 'checkpoint-group',
          data: group,
          position: index
        }));
      } else if (inspectionValue.components && Array.isArray(inspectionValue.components)) {
        // New format - components array
        components = inspectionValue.components;
      } else if (typeof inspectionValue === 'object') {
        // Check if this is an object with numbered keys (0, 1, 2, etc.)
        const keys = Object.keys(inspectionValue);
        const hasNumberedKeys = keys.some(key => !isNaN(Number(key)));

        if (hasNumberedKeys) {
          // Object with numbered keys - extract components
          components = Object.values(inspectionValue).filter((item: any) =>
            item && typeof item === 'object' && item.type && item.id
          );
        }
      }
    }

    // Sort components by position
    components.sort((a, b) => (a.position || 0) - (b.position || 0));

    if (components.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <FileText className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p>No checklist data available for this inspection.</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {components.map((component: any, index: number) => {
          const componentData = component.data || component;

          // Handle different component types
          if (component.type === 'header') {
            return (
              <div
                key={component.id || `header-${index}`}
                style={{
                  border: '1px solid #e7e9ec',
                  borderRadius: '8px',
                  padding: '15px',
                  marginBottom: '12px',
                  backgroundColor: '#fff'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                  {/* <Heading1 style={{ width: '24px', height: '24px', color: '#3b82f6' }} /> */}
                  <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: '#1f2937' }}>
                    {componentData.text}
                  </h2>
                </div>
                <div style={{
                  height: '2px',
                  background: 'linear-gradient(to right, #3b82f6, #93c5fd, transparent)',
                  marginTop: '4px'
                }}></div>
              </div>
            );
          }

          // Handle Section Header Component
          if (component.type === 'section-header') {
            return (
              <div
                key={component.id || `section-${index}`}
                style={{
                  border: '1px solid #e7e9ec',
                  borderRadius: '8px',
                  padding: '15px',
                  marginBottom: '12px',
                  backgroundColor: '#fff'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '6px' }}>
                  {/* <Heading2 style={{ width: '20px', height: '20px', color: '#6366f1' }} /> */}
                  <h3 style={{ fontSize: '16px', fontWeight: '600', color: '#4338ca' }}>
                    {componentData.text}
                  </h3>
                </div>
                <div style={{
                  height: '1px',
                  backgroundColor: '#c7d2fe',
                  marginTop: '2px'
                }}></div>
              </div>
            );
          }

          if (component.type === 'text-body') {
            return (
              <div
                key={component.id || `text-${index}`}
                style={{
                  border: '1px solid #e7e9ec',
                  borderRadius: '8px',
                  padding: '15px',
                  marginBottom: '12px',
                  backgroundColor: '#fff'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
                  <Type style={{ width: '20px', height: '20px', color: '#6b7280', marginTop: '2px', flexShrink: 0 }} />
                  <div style={{ flex: 1 }}>
                    <div style={{
                      fontSize: '14px',
                      color: '#2f2f2f',
                      lineHeight: '1.6',
                      whiteSpace: 'pre-wrap'
                    }}>
                      {componentData.content}
                    </div>
                  </div>
                </div>
              </div>
            );
          }

          // Handle Text Input Component
          if (component.type === 'text-input') {
            // Check for textValue at top level (new format) or in componentData (legacy format)
            const textValue = component.textValue || componentData.textValue;

            return (
              <div
                key={component.id || `text-input-${index}`}
                style={{
                  border: '1px solid #e7e9ec',
                  borderRadius: '8px',
                  padding: '15px',
                  marginBottom: '12px',
                  backgroundColor: '#fff'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
                  <TextCursor style={{ width: '20px', height: '20px', color: '#3b82f6', marginTop: '2px', flexShrink: 0 }} />
                  <div style={{ flex: 1 }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                      <span style={{ fontWeight: '500', color: '#2f2f2f', fontSize: '14px' }}>
                        {componentData.label}
                      </span>
                      {componentData.required && (
                        <span style={{ color: '#dc3545', fontSize: '12px' }}>*</span>
                      )}
                    </div>
                    <div style={{
                      backgroundColor: '#fcfcfc',
                      border: '1px solid #eee',
                      borderRadius: '5px',
                      padding: '10px'
                    }}>
                      <div style={{ fontSize: '14px', color: textValue ? '#2f2f2f' : '#999', fontStyle: textValue ? 'normal' : 'italic' }}>
                        {textValue || 'No response provided'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          }

          // Handle Attachment Input Component
          if (component.type === 'attachment-input') {
            // Check for uploads at top level (new format) or in componentData (legacy format)
            const uploads = component.uploads || componentData.uploads;

            return (
              <div
                key={component.id || `attachment-input-${index}`}
                style={{
                  border: '1px solid #e7e9ec',
                  borderRadius: '8px',
                  padding: '15px',
                  marginBottom: '12px',
                  backgroundColor: '#fff'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
                  <Paperclip style={{ width: '20px', height: '20px', color: '#14b8a6', marginTop: '2px', flexShrink: 0 }} />
                  <div style={{ flex: 1 }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                      <span style={{ fontWeight: '500', color: '#2f2f2f', fontSize: '14px' }}>
                        {componentData.label || 'File Upload'}
                      </span>
                      {componentData.required && (
                        <span style={{ color: '#dc3545', fontSize: '12px' }}>*</span>
                      )}
                    </div>
                    {uploads && uploads.length > 0 ? (
                      <div>
                        <span style={{ fontSize: '12px', fontWeight: '500', color: '#666', display: 'block', marginBottom: '8px' }}>
                          Uploaded Files:
                        </span>
                        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))', gap: '8px' }}>
                          {uploads.map((file: string, i: number) => (
                            <div key={i} style={{ border: '1px solid #ddd', borderRadius: '4px', overflow: 'hidden', backgroundColor: '#fff' }}>
                              <ImageComponent fileName={file} size="120" name={true} />
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div style={{
                        backgroundColor: '#f0fdfa',
                        border: '1px solid #5eead4',
                        borderRadius: '5px',
                        padding: '12px',
                        textAlign: 'center'
                      }}>
                        <div style={{ fontSize: '14px', color: '#0f766e', fontStyle: 'italic' }}>
                          No files uploaded
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          }

          // Handle Date Component
          if (component.type === 'date') {
            // Check for selectedDate at top level (new format) or in componentData (legacy format)
            const selectedDate = component.selectedDate || componentData.selectedDate;

            return (
              <div
                key={component.id || `date-${index}`}
                style={{
                  border: '1px solid #e7e9ec',
                  borderRadius: '8px',
                  padding: '15px',
                  marginBottom: '12px',
                  backgroundColor: '#fff'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
                  <Calendar style={{ width: '20px', height: '20px', color: '#8b5cf6', marginTop: '2px', flexShrink: 0 }} />
                  <div style={{ flex: 1 }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                      <span style={{ fontWeight: '500', color: '#2f2f2f', fontSize: '14px' }}>
                        {componentData.label}
                      </span>
                      {componentData.required && (
                        <span style={{ color: '#dc3545', fontSize: '12px' }}>*</span>
                      )}
                    </div>
                    <div style={{
                      backgroundColor: '#faf5ff',
                      border: '1px solid #d8b4fe',
                      borderRadius: '5px',
                      padding: '10px'
                    }}>
                      <div style={{ fontSize: '14px', color: selectedDate ? '#7c3aed' : '#999', fontStyle: selectedDate ? 'normal' : 'italic' }}>
                        {selectedDate ? formatDate(selectedDate) : 'No date selected'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          }

          // Handle Signature Component
          if (component.type === 'sign') {
            // Check for signature field at top level (new format) or in componentData (legacy format)
            const signature = component.signature || componentData.signature;

            return (
              <div
                key={component.id || `sign-${index}`}
                style={{
                  border: '1px solid #e7e9ec',
                  borderRadius: '8px',
                  padding: '15px',
                  marginBottom: '12px',
                  backgroundColor: '#fff'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
                  <PenTool style={{ width: '20px', height: '20px', color: '#f97316', marginTop: '2px', flexShrink: 0 }} />
                  <div style={{ flex: 1 }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                      <span style={{ fontWeight: '500', color: '#2f2f2f', fontSize: '14px' }}>
                        {componentData.label}
                      </span>
                      {componentData.required && (
                        <span style={{ color: '#dc3545', fontSize: '12px' }}>*</span>
                      )}
                    </div>
                    <div style={{
                      backgroundColor: '#fff7ed',
                      border: '1px solid #fed7aa',
                      borderRadius: '5px',
                      padding: '12px'
                    }}>
                      {signature ? (
                        <div style={{ textAlign: 'center' }}>
                          <div style={{ fontSize: '12px', color: '#ea580c', marginBottom: '8px' }}>
                            Digital Signature
                          </div>
                          <div style={{
                            display: 'inline-block',
                            border: '2px solid #fed7aa',
                            borderRadius: '8px',
                            padding: '8px',
                            backgroundColor: '#fff'
                          }}>
                            <div style={{ width: '300px', height: '150px' }}>
                              <ImageComponent fileName={signature} size="200" name={false} />
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div style={{ fontSize: '14px', color: '#999', textAlign: 'center', fontStyle: 'italic' }}>
                          No signature provided
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          }

          // Handle checkpoint groups - render exactly like test.html
          if (component.type === 'checkpoint-group' || componentData.label) {
            const groupTitle = componentData.title || componentData.label;

            // Check for group response fields at top level (new format) or in componentData (legacy format)
            const isChecked = component.isChecked !== undefined ? component.isChecked :
                              (componentData.isChecked || component.groupAnswer === 'Yes' || componentData.groupAnswer === 'Yes');

            // Check for checkpoints at top level (new format) or in componentData (legacy format)
            const checkpoints = component.checkpoints || componentData.checkpoints || componentData.questions || [];

            return (
              <div key={component.id || `group-${index}`} className="mb-6">
                {/* Tab header exactly like test.html */}
                <div style={{ display: 'flex', margin: '20px 0 10px' }}>
                  <div style={{
                    padding: '6px 15px',
                    fontSize: '13px',
                    borderRadius: '4px',
                    marginRight: '6px',
                    color: 'white',
                    backgroundColor: '#2ecc71',
                    fontWeight: '500'
                  }}>
                    {index + 1}. {groupTitle}
                  </div>
                </div>

                {/* Checklist items exactly like test.html */}
                {isChecked ? (
                  <div className="space-y-3">
                    {checkpoints.map((checkpoint: any, qIndex: number) => (
                      <div
                        key={`q-${qIndex}`}
                        style={{
                          border: '1px solid #e7e9ec',
                          borderRadius: '8px',
                          padding: '15px',
                          marginBottom: '12px',
                          backgroundColor: '#fff'
                        }}
                      >
                        <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
                          <div style={{ flex: 1, paddingRight: '16px' }}>
                            <div style={{ fontSize: '14px', fontWeight: '500', color: '#2f2f2f', lineHeight: '1.5' }}>
                              {checkpoint.text || checkpoint.label}
                            </div>
                          </div>
                          <div style={{ float: 'right' }}>
                            {checkpoint.selected === 'Yes' && (
                              <span style={{
                                padding: '4px 10px',
                                borderRadius: '5px',
                                fontSize: '13px',
                                border: '1px solid #c3e6cb',
                                backgroundColor: '#e6f4ea',
                                color: '#28a745',
                                cursor: 'default'
                              }}>
                                ✓ Yes
                              </span>
                            )}
                            {checkpoint.selected === 'No' && (
                              <span style={{
                                padding: '4px 10px',
                                borderRadius: '5px',
                                fontSize: '13px',
                                border: '1px solid #f5c6cb',
                                backgroundColor: '#fcebea',
                                color: '#dc3545',
                                cursor: 'default'
                              }}>
                                ✕ No
                              </span>
                            )}
                            {(checkpoint.selected === 'N/A' || !checkpoint.selected) && (
                              <span style={{
                                padding: '4px 10px',
                                borderRadius: '5px',
                                fontSize: '13px',
                                border: '1px solid #d6d8db',
                                backgroundColor: '#e2e3e5',
                                color: '#6c757d',
                                cursor: 'default'
                              }}>
                                N/A
                              </span>
                            )}
                          </div>
                        </div>

                        {checkpoint.remarks && (
                          <div style={{ marginTop: '10px' }}>
                            <div style={{
                              fontSize: '12px',
                              color: '#666',
                              marginTop: '10px',
                              display: 'flex',
                              alignItems: 'center'
                            }}>
                              <span style={{ marginRight: '5px' }}>ⓘ</span>
                              Remarks
                            </div>
                            <div style={{
                              marginTop: '5px',
                              padding: '10px',
                              backgroundColor: '#fcfcfc',
                              border: '1px solid #eee',
                              borderRadius: '5px',
                              color: '#555',
                              fontStyle: 'italic',
                              fontSize: '14px'
                            }}>
                              {checkpoint.remarks}
                            </div>
                          </div>
                        )}

                        {/* Action Required Section */}
                        {checkpoint.actionToBeTaken && (
                          <div style={{ marginTop: '15px' }}>
                            <div style={{
                              fontSize: '12px',
                              color: '#dc3545',
                              fontWeight: '600',
                              marginBottom: '8px',
                              display: 'flex',
                              alignItems: 'center'
                            }}>
                              <span style={{ marginRight: '5px' }}>⚠</span>
                              Action Required
                            </div>
                            <div style={{
                              padding: '12px',
                              backgroundColor: '#fff5f5',
                              border: '1px solid #fed7d7',
                              borderRadius: '5px',
                              borderLeft: '4px solid #dc3545'
                            }}>
                              <div style={{ fontSize: '14px', color: '#2d3748', marginBottom: '8px' }}>
                                <strong>Action:</strong> {checkpoint.actionToBeTaken}
                              </div>
                              {checkpoint.dueDate && (
                                <div style={{ fontSize: '12px', color: '#dc3545', marginBottom: '4px' }}>
                                  <strong>Due Date:</strong> {formatDate(checkpoint.dueDate)}
                                </div>
                              )}
                              {checkpoint.assignee && (
                                <div style={{ fontSize: '12px', color: '#dc3545' }}>
                                  <strong>Assigned to:</strong> {getName(checkpoint.assignee)}
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        {checkpoint.uploads && checkpoint.uploads.length > 0 && (
                          <div style={{ marginTop: '15px' }}>
                            <span style={{ fontSize: '12px', fontWeight: '500', color: '#666', display: 'block', marginBottom: '8px' }}>
                              Evidence:
                            </span>
                            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '8px' }}>
                              {checkpoint.uploads.map((file: string, i: number) => (
                                <div key={i} style={{ border: '1px solid #ddd', borderRadius: '4px', overflow: 'hidden' }}>
                                  <ImageComponent fileName={file} size="80" name={false} />
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div style={{
                    backgroundColor: '#f8f9fa',
                    border: '1px solid #dee2e6',
                    borderRadius: '5px',
                    padding: '15px'
                  }}>
                    <p style={{ fontSize: '14px', color: '#6c757d', margin: 0 }}>
                      This checkpoint group was not completed.
                    </p>
                  </div>
                )}
              </div>
            );
          }

          // Handle individual checkpoints (not in groups)
          if (component.type === 'checkpoint') {
            const selected = component.selected || componentData.selected;
            const remarks = component.remarks || componentData.remarks;
            const actionToBeTaken = component.actionToBeTaken || componentData.actionToBeTaken;
            const dueDate = component.dueDate || componentData.dueDate;
            const assignee = component.assignee || componentData.assignee;
            const uploads = component.uploads || componentData.uploads;

            return (
              <div
                key={component.id || `checkpoint-${index}`}
                style={{
                  border: '1px solid #e7e9ec',
                  borderRadius: '8px',
                  padding: '15px',
                  marginBottom: '12px',
                  backgroundColor: '#fff'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
                  <div style={{ flex: 1, paddingRight: '16px' }}>
                    <div style={{ fontSize: '14px', fontWeight: '500', color: '#2f2f2f', lineHeight: '1.5' }}>
                      {componentData.text}
                    </div>
                  </div>
                  <div style={{ float: 'right' }}>
                    {selected === 'Yes' && (
                      <span style={{
                        padding: '4px 10px',
                        borderRadius: '5px',
                        fontSize: '13px',
                        border: '1px solid #c3e6cb',
                        backgroundColor: '#e6f4ea',
                        color: '#28a745',
                        cursor: 'default'
                      }}>
                        ✓ Yes
                      </span>
                    )}
                    {selected === 'No' && (
                      <span style={{
                        padding: '4px 10px',
                        borderRadius: '5px',
                        fontSize: '13px',
                        border: '1px solid #f5c6cb',
                        backgroundColor: '#fcebea',
                        color: '#dc3545',
                        cursor: 'default'
                      }}>
                        ✕ No
                      </span>
                    )}
                    {(selected === 'N/A' || !selected) && (
                      <span style={{
                        padding: '4px 10px',
                        borderRadius: '5px',
                        fontSize: '13px',
                        border: '1px solid #d6d8db',
                        backgroundColor: '#e2e3e5',
                        color: '#6c757d',
                        cursor: 'default'
                      }}>
                        N/A
                      </span>
                    )}
                  </div>
                </div>

                {remarks && (
                  <div style={{ marginTop: '10px' }}>
                    <div style={{
                      fontSize: '12px',
                      color: '#666',
                      marginTop: '10px',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <span style={{ marginRight: '5px' }}>ⓘ</span>
                      Remarks
                    </div>
                    <div style={{
                      marginTop: '5px',
                      padding: '10px',
                      backgroundColor: '#fcfcfc',
                      border: '1px solid #eee',
                      borderRadius: '5px',
                      color: '#555',
                      fontStyle: 'italic',
                      fontSize: '14px'
                    }}>
                      {remarks}
                    </div>
                  </div>
                )}

                {/* Action Required Section */}
                {actionToBeTaken && (
                  <div style={{ marginTop: '15px' }}>
                    <div style={{
                      fontSize: '12px',
                      color: '#dc3545',
                      fontWeight: '600',
                      marginBottom: '8px',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <span style={{ marginRight: '5px' }}>⚠</span>
                      Action Required
                    </div>
                    <div style={{
                      padding: '12px',
                      backgroundColor: '#fff5f5',
                      border: '1px solid #fed7d7',
                      borderRadius: '5px',
                      borderLeft: '4px solid #dc3545'
                    }}>
                      <div style={{ fontSize: '14px', color: '#2d3748', marginBottom: '8px' }}>
                        <strong>Action:</strong> {actionToBeTaken}
                      </div>
                      {dueDate && (
                        <div style={{ fontSize: '12px', color: '#dc3545', marginBottom: '4px' }}>
                          <strong>Due Date:</strong> {formatDate(dueDate)}
                        </div>
                      )}
                      {assignee && (
                        <div style={{ fontSize: '12px', color: '#dc3545' }}>
                          <strong>Assigned to:</strong> {getName(assignee)}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {uploads && uploads.length > 0 && (
                  <div style={{ marginTop: '15px' }}>
                    <span style={{ fontSize: '12px', fontWeight: '500', color: '#666', display: 'block', marginBottom: '8px' }}>
                      Evidence:
                    </span>
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '8px' }}>
                      {uploads.map((file: string, i: number) => (
                        <div key={i} style={{ border: '1px solid #ddd', borderRadius: '4px', overflow: 'hidden' }}>
                          <ImageComponent fileName={file} size="80" name={false} />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            );
          }

          // Default fallback for unknown component types
          return (
            <div key={component.id || `unknown-${index}`} className="mb-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
              <div className="flex items-center gap-3">
                <AlertCircle className="w-5 h-5 text-gray-500" />
                <div>
                  <p className="font-medium text-gray-800">Component Type: {component.type}</p>
                  <p className="text-sm text-gray-600">This component type is displayed in a simplified format.</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[1200px] max-h-[90vh] overflow-y-auto"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-2xl font-bold">
                Inspection Report
              </DialogTitle>
            </div>
            <div className="flex items-center space-x-3 no-print">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrint}
                className="bg-green-50 hover:bg-green-100 border-green-300 text-green-700"
              >
                <Printer className="h-4 w-4 mr-2" />
                Print Report
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Printable Content */}
        <div ref={printRef} className="print-content">
          {/* Print Styles */}
          <style>{`
            @media print {
              * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
              }

              .print-content {
                font-family: Arial, sans-serif !important;
                font-size: 12px !important;
                line-height: 1.4 !important;
                color: #000 !important;
              }

              .print-only {
                display: block !important;
              }

              .no-print {
                display: none !important;
              }

              /* Preserve borders and backgrounds */
              div[style*="border"] {
                border: 1px solid #e7e9ec !important;
                border-radius: 8px !important;
                padding: 15px !important;
                margin-bottom: 12px !important;
                background-color: #fff !important;
              }

              /* Tab styling */
              div[style*="background-color: #2ecc71"] {
                background-color: #2ecc71 !important;
                color: white !important;
                padding: 6px 15px !important;
                border-radius: 4px !important;
                font-size: 13px !important;
                font-weight: 500 !important;
              }

              /* Response buttons */
              span[style*="background-color: #e6f4ea"] {
                background-color: #e6f4ea !important;
                color: #28a745 !important;
                border: 1px solid #c3e6cb !important;
                padding: 4px 10px !important;
                border-radius: 5px !important;
                font-size: 13px !important;
              }

              span[style*="background-color: #fcebea"] {
                background-color: #fcebea !important;
                color: #dc3545 !important;
                border: 1px solid #f5c6cb !important;
                padding: 4px 10px !important;
                border-radius: 5px !important;
                font-size: 13px !important;
              }

              span[style*="background-color: #e2e3e5"] {
                background-color: #e2e3e5 !important;
                color: #6c757d !important;
                border: 1px solid #d6d8db !important;
                padding: 4px 10px !important;
                border-radius: 5px !important;
                font-size: 13px !important;
              }

              /* Remarks section */
              div[style*="background-color: #fcfcfc"] {
                background-color: #fcfcfc !important;
                border: 1px solid #eee !important;
                border-radius: 5px !important;
                padding: 10px !important;
                color: #555 !important;
                font-style: italic !important;
              }

              /* Action required section */
              div[style*="background-color: #fff5f5"] {
                background-color: #fff5f5 !important;
                border: 1px solid #fed7d7 !important;
                border-left: 4px solid #dc3545 !important;
                border-radius: 5px !important;
                padding: 12px !important;
              }

              /* Action cards */
              div[style*="border: 1px solid #e0e0e0"] {
                border: 1px solid #e0e0e0 !important;
                padding: 15px !important;
                border-radius: 8px !important;
                margin-bottom: 10px !important;
                background-color: #fff !important;
              }

              /* Action detail headers */
              div[style*="background-color: #199653"] {
                background-color: #199653 !important;
                color: white !important;
                padding: 1rem 1.5rem !important;
                font-size: 1.2rem !important;
                font-weight: bold !important;
              }

              /* Grid layouts */
              .grid {
                display: grid !important;
              }

              .grid-cols-3 {
                grid-template-columns: repeat(3, 1fr) !important;
                gap: 1.5rem !important;
              }

              .grid-cols-1 {
                grid-template-columns: 1fr !important;
              }

              .grid-cols-2 {
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 1rem !important;
              }

              .grid-cols-4 {
                grid-template-columns: repeat(4, 1fr) !important;
                gap: 0.5rem !important;
              }

              /* Flex layouts */
              .flex {
                display: flex !important;
              }

              .items-center {
                align-items: center !important;
              }

              .justify-between {
                justify-content: space-between !important;
              }

              .space-y-3 > * + * {
                margin-top: 0.75rem !important;
              }

              .space-y-4 > * + * {
                margin-top: 1rem !important;
              }

              .space-y-6 > * + * {
                margin-top: 1.5rem !important;
              }

              /* Badge styles */
              .bg-green-500 {
                background-color: #10b981 !important;
                color: white !important;
                padding: 2px 8px !important;
                border-radius: 4px !important;
                font-size: 12px !important;
              }

              .bg-blue-500 {
                background-color: #3b82f6 !important;
                color: white !important;
                padding: 2px 8px !important;
                border-radius: 4px !important;
                font-size: 12px !important;
              }

              .bg-yellow-500 {
                background-color: #f59e0b !important;
                color: white !important;
                padding: 2px 8px !important;
                border-radius: 4px !important;
                font-size: 12px !important;
              }

              /* Text sizes */
              .text-xs {
                font-size: 0.75rem !important;
              }

              .text-sm {
                font-size: 0.875rem !important;
              }

              .text-lg {
                font-size: 1.125rem !important;
              }

              .text-xl {
                font-size: 1.25rem !important;
              }

              /* Font weights */
              .font-medium {
                font-weight: 500 !important;
              }

              .font-semibold {
                font-weight: 600 !important;
              }

              .font-bold {
                font-weight: 700 !important;
              }

              /* Colors */
              .text-gray-500 {
                color: #6b7280 !important;
              }

              .text-gray-600 {
                color: #4b5563 !important;
              }

              .text-gray-700 {
                color: #374151 !important;
              }

              .text-gray-800 {
                color: #1f2937 !important;
              }

              /* Page breaks */
              .page-break {
                page-break-before: always !important;
              }

              /* Avoid breaking inside elements */
              div[style*="border: 1px solid #e7e9ec"] {
                page-break-inside: avoid !important;
              }
            }
          `}</style>

          {/* Print-only header */}
          <div className="print-only" style={{ display: 'none' }}>
            <div className="text-center mb-8">
              {logo && (
                <img
                  src={logo}
                  alt="Company Logo"
                  style={{ width: '80px', height: 'auto', margin: '0 auto 20px' }}
                />
              )}
              <h1 style={{ fontSize: '28px', fontWeight: 'bold', color: '#1f2937', margin: '0 0 10px' }}>
                INSPECTION REPORT
              </h1>
              <p style={{ fontSize: '16px', color: '#6b7280', fontStyle: 'italic', margin: '0 0 20px' }}>
                Inspection ID: {inspection.maskId}
              </p>
              <div style={{ height: '2px', background: '#3b82f6', margin: '20px 0' }}></div>
            </div>
          </div>



          <div className="space-y-6">
            {/* Header Container - Following test.html structure */}
            <div className="relative mb-6">
              <div className="flex justify-between items-start mb-5">
                <div className="flex items-center">
                  <FileText className="w-5 h-5 mr-2" />
                  <h1 className="text-xl font-semibold">Inspection Report</h1>
                </div>
                <div className="flex flex-col items-end">
                  {getStatusBadge(inspection.status)}
                  <div className="text-xs text-gray-500 mt-1 flex items-center">
                    <Clock className="w-3 h-3 mr-1" />
                    {formatDate(inspection.completedDate) !== "N/A"
                      ? formatDate(inspection.completedDate)
                      : formatDate(inspection.scheduledDate)}
                  </div>
                </div>
              </div>
            </div>

            {/* Header Section - 3 column grid like test.html */}
            <div className="grid grid-cols-3 gap-6 border-b border-gray-200 pb-6 mb-6">
              <div className="space-y-4">
                <div>
                  <div className="text-xs text-gray-500 mb-1">Checklist Name</div>
                  <div className="font-semibold text-sm">{inspection.checklist?.name || inspection.title || "N/A"}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 mb-1">Location</div>
                  <div className="font-semibold text-sm">{locationDisplay}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 mb-1">Assigned Date</div>
                  <div className="font-semibold text-sm">{formatDate(inspection.created)}</div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <div className="text-xs text-gray-500 mb-1">Type</div>
                  <div className="font-semibold text-sm">{inspection.checklist?.category || "N/A"}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 mb-1">Date of Inspection</div>
                  <div className="font-semibold text-sm">{formatDate(inspection.completedDate) || formatDate(inspection.scheduledDate)}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 mb-1">Start Date</div>
                  <div className="font-semibold text-sm">{formatDate(inspection.scheduledDate)}</div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <div className="text-xs text-gray-500 mb-1">Reference #</div>
                  <div className="font-semibold text-sm">{inspection.maskId}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 mb-1">Due Date</div>
                  <div className="font-semibold text-sm">{formatDate(inspection.scheduledDate)}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 mb-1">Assigned to</div>
                  <div className="font-semibold text-sm">{inspection.inspector?.firstName || "N/A"}</div>
                </div>
              </div>
            </div>

            {/* Description */}
            {inspection.description && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-3">Description</h3>
                <div className="bg-gray-50 border rounded-lg p-4">
                  <p className="text-sm leading-relaxed">{inspection.description}</p>
                </div>
              </div>
            )}

            {/* Checklist Results */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Inspection Checklist</h3>
              {renderChecklistComponents()}
            </div>

            {/* Actions Section - Using totalActions like ActionLogModal */}
            {(() => {
              // Get totalActions from inspection
              const totalActions = (inspection as { totalActions?: any[] })?.totalActions || [];
              const processedActionData = groupByDescription(totalActions);

              // Filter and process actions like ActionLogModal
              let actionCounter = 0.0;
              const processedActions = processedActionData
                .map(action => {
                  if (action.firstActionType === 'perform_task') {
                    actionCounter += 1.0;
                    return { ...action, actionNumber: actionCounter };
                  }
                  return null;
                })
                .filter(action => action !== null);

              if (processedActions.length === 0) return null;

              return (
                <div className="mt-8">
                  <h3 style={{ fontSize: '18px', margin: '30px 0 10px', fontWeight: 'bold' }}>
                    {inspection?.maskId} Actions
                  </h3>

                  {/* Action Cards */}
                  {/* <div className="space-y-3">
                    {processedActions.map((action: any, index: number) => {
                      const latestAction = action.data[action.data.length - 1];
                      return (
                        <div
                          key={index}
                          style={{
                            border: '1px solid #e0e0e0',
                            padding: '15px',
                            borderRadius: '8px',
                            marginBottom: '10px',
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            backgroundColor: '#fff'
                          }}
                        >
                          <div style={{ flexGrow: 1 }}>
                            <div style={{ fontSize: '12px', color: '#666', marginBottom: '3px' }}>
                              IA {action.actionNumber.toFixed(1)}
                            </div>
                            <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
                              {latestAction.actionToBeTaken || latestAction.description}
                            </div>
                            <div style={{ fontSize: '12px', color: '#444' }}>
                              Due: <strong>{latestAction.dueDate ? formatDate(latestAction.dueDate) : 'Not specified'}</strong>
                              {latestAction.assignedToId && (
                                <>
                                  &nbsp;&nbsp;Assigned to: <strong>{getName(latestAction.assignedToId)}</strong>
                                </>
                              )}
                            </div>
                          </div>
                          <div style={{
                            fontSize: '12px',
                            fontWeight: '600',
                            padding: '4px 10px',
                            borderRadius: '12px',
                            display: 'inline-block',
                            whiteSpace: 'nowrap',
                            backgroundColor: action.lastStatus === 'Completed' ? '#d4edda' : '#fff3cd',
                            color: action.lastStatus === 'Completed' ? '#155724' : '#856404'
                          }}>
                            {action.lastStatus || 'Pending'}
                          </div>
                        </div>
                      );
                    })}
                  </div> */}

                  {/* Action Details Section - Exactly like ActionLogModal */}
                  <div className="mt-8">
                    <h3 style={{ fontSize: '18px', margin: '30px 0 10px', fontWeight: 'bold' }}>
                      Action Details
                    </h3>

                    {processedActions.length > 0 ? (
                      <div className="space-y-2">
                        {processedActions.map((action: any, index: number) => (
                          <div key={index} className="border rounded-lg">
                            {/* Action Header - Like ActionLogModal AccordionTrigger */}
                            <div className="px-4 py-3 bg-gray-50 border-b">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <span className="font-medium">IA {action.actionNumber.toFixed(1)}</span>
                                  {getActionStatusBadge(action)}
                                </div>
                                <div className="text-sm text-gray-600">
                                  {getName(action.lastAssignedToId)}
                                </div>
                              </div>
                            </div>

                            {/* Action Details - Like ActionLogModal AccordionContent */}
                            <div className="px-4 pb-4 pt-4">
                              {action.data.map((item: any, itemIndex: number) => (
                                <div key={itemIndex} className="mb-4 border border-gray-200 rounded-lg">
                                  {/* Card Header */}
                                  <div className="px-4 py-3 border-b bg-gray-50">
                                    <div className="text-sm flex items-center gap-2">
                                      {item.actionType === 'perform_task' && (
                                        <>
                                          <FileText className="h-4 w-4" />
                                          Assigned Action - IA {action.actionNumber.toFixed(1)}
                                        </>
                                      )}
                                      {item.actionType === 'verify_task' && (
                                        <>
                                          <CheckCircle className="h-4 w-4" />
                                          Action Verification - IA {action.actionNumber.toFixed(1)}
                                        </>
                                      )}
                                      {item.actionType === 'reperform_task' && (
                                        <>
                                          <AlertCircle className="h-4 w-4" />
                                          Action Re-assignment - IA {action.actionNumber.toFixed(1)}
                                        </>
                                      )}
                                    </div>
                                  </div>

                                  {/* Card Content */}
                                  <div className="p-4 space-y-3">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                      <div>
                                        {item.actionToBeTaken && (
                                          <div className="space-y-1">
                                            <p className="text-sm font-medium text-gray-700">Action to be Taken:</p>
                                            <p className="text-sm text-gray-600">{item.actionToBeTaken}</p>
                                          </div>
                                        )}
                                        {item.actionTaken && (
                                          <div className="space-y-1">
                                            <p className="text-sm font-medium text-gray-700">Action Taken:</p>
                                            <p className="text-sm text-gray-600">{item.actionTaken}</p>
                                          </div>
                                        )}
                                        {item.comments && (
                                          <div className="space-y-1">
                                            <p className="text-sm font-medium text-gray-700">Comments:</p>
                                            <p className="text-sm text-gray-600">{item.comments}</p>
                                          </div>
                                        )}
                                        <div className="space-y-1">
                                          <p className="text-sm font-medium text-gray-700">
                                            {item.status === 'Initiated' ? 'Action Assignee:' : 'Action Taken By:'}
                                          </p>
                                          <div className="flex items-center gap-2">
                                            <UserIcon className="h-4 w-4 text-gray-500" />
                                            <p className="text-sm text-gray-600">
                                              {item.assignedToId && getName(
                                                Array.isArray(item.assignedToId) ? item.assignedToId[0] : item.assignedToId
                                              )}
                                            </p>
                                          </div>
                                        </div>
                                      </div>
                                      <div className="space-y-3">
                                        <div className="space-y-1">
                                          <p className="text-sm font-medium text-gray-700">Date:</p>
                                          <div className="flex items-center gap-2">
                                            <Calendar className="h-4 w-4 text-gray-500" />
                                            <p className="text-sm text-gray-600">
                                              {item.created ? formatDate(item.created) : 'N/A'}
                                            </p>
                                          </div>
                                        </div>
                                        {item.dueDate && (
                                          <div className="space-y-1">
                                            <p className="text-sm font-medium text-gray-700">Due Date:</p>
                                            <div className="flex items-center gap-2">
                                              <Clock className="h-4 w-4 text-gray-500" />
                                              <p className="text-sm text-gray-600">
                                                {formatDate(item.dueDate)}
                                              </p>
                                            </div>
                                          </div>
                                        )}
                                        <div className="space-y-1">
                                          <p className="text-sm font-medium text-gray-700">Status:</p>
                                          <Badge variant={item.status === 'Completed' ? 'default' : 'outline'}>
                                            {item.status || 'Pending'}
                                          </Badge>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <p>No actions found for this inspection.</p>
                      </div>
                    )}
                  </div>
                </div>
              );
            })()}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};



export default InspectionDetailsModal;
