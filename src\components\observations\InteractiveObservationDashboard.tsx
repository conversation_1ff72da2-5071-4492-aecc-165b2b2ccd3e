import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Observation } from '@/types/observation';
import {
  ClipboardList,
  CheckCircle,
  AlertCircle,
  Clock,
  ChevronDown,
  ChevronUp,
  Filter,
  RefreshCw
} from 'lucide-react';
import { format, isBefore } from 'date-fns';
import ObservationStatusTable from './ObservationStatusTable';
import { cn } from '@/lib/utils';

// Define the status types for filtering
export type ObservationStatusFilter = 'all' | 'open' | 'overdue' | 'completed';

interface MetricCardProps {
  title: string;
  description: string;
  value: number | string;
  icon: React.ReactNode;
  colorClass: string;
  onClick: () => void;
  isActive: boolean;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  description,
  value,
  icon,
  colorClass,
  onClick,
  isActive
}) => (
  <Card
    className={cn(
      "border transition-all duration-200 cursor-pointer",
      isActive ? "ring-2 ring-primary shadow-md" : "hover:shadow-md"
    )}
    onClick={onClick}
  >
    <CardHeader className="pb-2">
      <CardTitle className="text-lg flex items-center gap-2">
        {icon}
        {title}
      </CardTitle>
      <CardDescription>{description}</CardDescription>
    </CardHeader>
    <CardContent>
      <div className={`text-3xl font-bold ${colorClass}`}>{value}</div>
      <div className="text-xs text-gray-500 mt-1">
        {(() => {
          const now = new Date();
          const previousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
          return previousMonth.toLocaleDateString('en-US', {
            month: 'long',
            year: 'numeric'
          });
        })()}
      </div>
    </CardContent>
  </Card>
);

interface InteractiveObservationDashboardProps {
  observations: Observation[];
}

const InteractiveObservationDashboard: React.FC<InteractiveObservationDashboardProps> = ({
  observations
}) => {
  const [expandedView, setExpandedView] = useState<boolean>(false);
  const [activeFilter, setActiveFilter] = useState<ObservationStatusFilter>('all');

  // Calculate key metrics
  const totalObservations = observations.length;
  const openObservations = observations.filter(obs =>
    obs.status === 'Open' || obs.status === 'In Progress' || obs.status === 'Pending Review'
  ).length;
  const overdueObservations = observations.filter(obs =>
    obs.dueDate && isBefore(new Date(obs.dueDate), new Date()) &&
    (obs.status === 'Open' || obs.status === 'In Progress' || obs.status === 'Pending Review')
  ).length;
  const completionRate = Math.round((observations.filter(obs => obs.status === 'Closed' || obs.status === 'Action Completed & Closed').length / totalObservations) * 100);

  // Filter observations based on active filter
  const getFilteredObservations = () => {
    switch(activeFilter) {
      case 'open':
        return observations.filter(obs =>
          obs.status === 'Open' || obs.status === 'In Progress' || obs.status === 'Pending Review'
        );
      case 'overdue':
        return observations.filter(obs =>
          obs.dueDate && isBefore(new Date(obs.dueDate), new Date()) &&
          (obs.status === 'Open' || obs.status === 'In Progress' || obs.status === 'Pending Review')
        );
      case 'completed':
        return observations.filter(obs => obs.status === 'Closed' || obs.status === 'Action Completed & Closed');
      default:
        return observations;
    }
  };

  const toggleExpandedView = () => {
    setExpandedView(!expandedView);
  };

  return (
    <div className="space-y-4">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="New Observations"
          description="All observations"
          value={totalObservations}
          icon={<ClipboardList className="h-5 w-5 text-primary" />}
          colorClass="text-primary"
          onClick={() => {
            setActiveFilter('all');
            setExpandedView(true);
          }}
          isActive={activeFilter === 'all' && expandedView}
        />

        <MetricCard
          title="Open Actions"
          description="Requiring attention"
          value={openObservations}
          icon={<Clock className="h-5 w-5 text-warning-500" />}
          colorClass="text-warning-500"
          onClick={() => {
            setActiveFilter('open');
            setExpandedView(true);
          }}
          isActive={activeFilter === 'open' && expandedView}
        />

        <MetricCard
          title="Overdue"
          description="Past due date"
          value={overdueObservations}
          icon={<AlertCircle className="h-5 w-5 text-destructive" />}
          colorClass="text-destructive"
          onClick={() => {
            setActiveFilter('overdue');
            setExpandedView(true);
          }}
          isActive={activeFilter === 'overdue' && expandedView}
        />

        <MetricCard
          title="Completion Rate"
          description="Closed observations"
          value={`${completionRate}%`}
          icon={<CheckCircle className="h-5 w-5 text-success-500" />}
          colorClass="text-success-500"
          onClick={() => {
            setActiveFilter('completed');
            setExpandedView(true);
          }}
          isActive={activeFilter === 'completed' && expandedView}
        />
      </div>

      {/* Expanded View with Filtered Observations */}
      {expandedView && (
        <Card className="border animate-in fade-in-50 duration-300">
          <CardHeader className="pb-2 flex flex-row items-center justify-between">
            <div>
              <CardTitle className="text-lg flex items-center gap-2">
                Pending Observations
                <Badge variant="outline" className="ml-2">
                  {getFilteredObservations().length} items
                </Badge>
              </CardTitle>
              <CardDescription>
                {activeFilter === 'all' && 'All observations'}
                {activeFilter === 'open' && 'Observations requiring attention'}
                {activeFilter === 'overdue' && 'Observations past due date'}
                {activeFilter === 'completed' && 'Closed observations'}
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={toggleExpandedView}>
              <ChevronUp className="h-4 w-4" />
            </Button>
          </CardHeader>
          <CardContent>
            <ObservationStatusTable observations={getFilteredObservations()} />
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default InteractiveObservationDashboard;
