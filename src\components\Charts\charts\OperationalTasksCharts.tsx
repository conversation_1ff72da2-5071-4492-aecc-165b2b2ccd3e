import { useState, useMemo } from "react";
import MetricCard from "@/components/Charts/MetricCard";
import MetricDetails from "@/components/Charts/MetricDetails";
import { LocationFilterState } from "@/types/locationFilter";


interface OperationalTasksChartsProps {
  locationFilters?: LocationFilterState;
}

const OperationalTasksCharts = ({ locationFilters }: OperationalTasksChartsProps) => {
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});


  // Operational Tasks metrics data
  const overviewMetrics = [
    {
      id: "tasks-completed-on-time",
      title: "% of tasks completed On Time",
      value: "91",
      unit: "tasks (91%)",
      target: 100,
      targetPercentage: 9,
      trend: [87, 88, 89, 90, 91, 92, 91, 90, 91, 92, 93, 91],
      isImproving: true
    },
    {
      id: "tasks-overdue",
      title: "No. of tasks that are overdue",
      value: "12",
      unit: "tasks",
      target: 0,
      targetPercentage: 100,
      trend: [18, 16, 15, 14, 12, 10, 12, 14, 12, 10, 12, 12],
      isImproving: true
    },
    {
      id: "first-time-right-completion",
      title: "First-time-right task completion ratio",
      value: "85",
      unit: "tasks (85%)",
      target: 100,
      targetPercentage: 15,
      trend: [80, 81, 82, 83, 84, 85, 84, 83, 84, 85, 86, 85],
      isImproving: true
    },
    {
      id: "tasks-initiated-vs-closed",
      title: "Ratio of Tasks Initiated vs. Closed",
      value: "1.2",
      unit: "ratio",
      target: 1,
      targetPercentage: 20,
      trend: [1.5, 1.4, 1.3, 1.2, 1.1, 1.2, 1.3, 1.2, 1.1, 1.2, 1.3, 1.2],
      isImproving: true
    }
  ];

  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  return (
    <div className="space-y-6">

      {/* Headline Metrics */}
      <div className="grid grid-cols-4 gap-4">
        {overviewMetrics.map((metric) => (
          <MetricCard
            key={metric.id}
            {...metric}
            showDetails={openDetailsId === metric.id}
            onToggleDetails={() => handleToggleDetails(metric.id)}
            selectedPeriod={getSelectedPeriod(metric.id)}
            onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
          />
        ))}
      </div>

      {/* Details Section - Rendered below the grid when a card is selected */}
      {openDetailsId && (() => {
        const selectedMetric = overviewMetrics.find(m => m.id === openDetailsId);
        return selectedMetric ? (
          <MetricDetails
            title={selectedMetric.title}
            value={selectedMetric.value}
            unit={selectedMetric.unit}
            targetPercentage={selectedMetric.targetPercentage}
            trend={selectedMetric.trend}
            isImproving={selectedMetric.isImproving}
            selectedPeriod={getSelectedPeriod(openDetailsId)}
          />
        ) : null;
      })()}
    </div>
  );
};

export default OperationalTasksCharts;
