
import { But<PERSON> } from "@/components/ui/button";
import { 
  <PERSON>, 
  CardContent, 
  CardDes<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";

interface ActionCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  count: number;
  link: string;
  tooltipText: string;
  color?: string;
}

const ActionCard = ({
  title,
  description,
  icon,
  count,
  link,
  tooltipText,
  color
}: ActionCardProps) => {
  return (
    <Card
      className="overflow-hidden transition-all duration-300 hover:shadow-md hover:-translate-y-1 relative"
      style={{
        borderTop: color ? `6px solid ${color}` : undefined
      }}
    >
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <div
              className="p-1.5 rounded-md"
              style={{
                backgroundColor: color ? `${color}15` : undefined, // 15 is hex for ~8% opacity
                color: color || undefined
              }}
            >
              {icon}
            </div>
            {title}
          </CardTitle>
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="badge badge-primary text-lg font-bold">{count}</div>
              </TooltipTrigger>
              <TooltipContent>{count} pending actions</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      {/* <CardContent>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <p className="text-sm text-muted-foreground">
                {tooltipText}
              </p>
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">{tooltipText}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </CardContent> */}
      <CardFooter>
        <Button asChild variant="ghost" className="ml-auto gap-1 hover:gap-2 transition-all">
          <Link to={link}>
            View <ArrowRight className="h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ActionCard;
