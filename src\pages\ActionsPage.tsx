
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import PageHeader from "@/components/common/PageHeader";
import ActionCard from "@/components/common/ActionCard";
import {
  ClipboardList,
  FileWarning,
  Clipboard,
  FileCheck,
  AlertTriangle,
  CheckSquare,
  BookOpen,
  Loader2,
  Settings
} from 'lucide-react';
import { fetchServices, fetchAssignedActions, Service, Action } from "@/services/api";
import { useToast } from "@/components/ui/use-toast";

// Map service names to icons
const getIconForService = (serviceName: string) => {
  switch (serviceName) {
    case "Observation Reporting":
      return <ClipboardList className="h-5 w-5" />;
    case "Integrated Risk Assessment":
      return <FileWarning className="h-5 w-5" />;
    case "ePermit to Work":
      return <FileCheck className="h-5 w-5" />;
    case "Operational Tasks":
      return <Clipboard className="h-5 w-5" />;
    case "Incident Investigation":
      return <AlertTriangle className="h-5 w-5" />;
    case "Inspection":
      return <CheckSquare className="h-5 w-5" />;
    case "Knowledge":
      return <BookOpen className="h-5 w-5" />;
    case "Change Management":
      return <Settings className="h-5 w-5" />;
    default:
      return <ClipboardList className="h-5 w-5" />;
  }
};

const ActionsPage = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [actions, setActions] = useState<Action[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { accessToken } = useSelector((state: RootState) => state.auth);
  const { toast } = useToast();

  useEffect(() => {
    const loadData = async () => {
      if (!accessToken) {
        setError("Authentication token not found");
        setIsLoading(false);
        return;
      }

      try {
        // Fetch both services and actions in parallel
        const [servicesData, actionsData] = await Promise.all([
          fetchServices(accessToken),
          fetchAssignedActions(accessToken)
        ]);

        setServices(servicesData);
        setActions(actionsData);
        setIsLoading(false);
      } catch (err) {
        console.error("Error fetching data:", err);
        setError("Failed to load data. Please try again.");
        toast({
          title: "Error",
          description: "Failed to load data. Please try again.",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    loadData();
  }, [accessToken, toast]);

  // Function to get the count of actions for a specific service
  const getActionCountForService = (service: Service): number => {
    const apiActionCount = actions.filter(action => action.application === service.maskName).length;

    // Special handling for Change Management - add mock actions count if service matches
    if (service.name === "Change Management" || service.url === "/apps/cgm") {
      // Add 6 mock actions for Change Management (from ChangeManagementPage.tsx)
      return apiActionCount + 6;
    }

    return apiActionCount;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
          <h2 className="mt-4 text-xl font-semibold">Loading data...</h2>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <div className="text-center max-w-md">
          <div className="bg-destructive/10 text-destructive p-4 rounded-md mb-4">
            <h2 className="font-semibold mb-2">Error Loading Data</h2>
            <p>{error}</p>
          </div>
          <button
            className="btn btn-primary"
            onClick={() => window.location.reload()}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <PageHeader
        title="My Actions"
        description="View and manage all your assigned tasks and action items."
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 animate-fade-in">
        {services.map((service) => (
          <ActionCard
            key={service.id}
            title={service.name}
            description={service.description}
            icon={getIconForService(service.name)}
            count={getActionCountForService(service)}
            link={service.url}
            tooltipText={service.description}
            color={service.color}
          />
        ))}
      </div>
    </>
  );
};

export default ActionsPage;
