import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  FileText,
  Users,
  Calendar,
  AlertTriangle,
  Shield,
  CheckCircle,
  Clock,
  User,
  Building,
  Activity,
  Loader2,
  Image as ImageIcon,
  Paperclip
} from 'lucide-react';
import { formatDate } from '@/utils/dateUtils';
import apiService from '@/services/apiService';
import SubActivityDetailDialog from './SubActivityDetailDialog';
import ImageComponent from '@/components/common/ImageComponent';
import GenerateLandPdf from './Component/PDF';
import UpdateTable from './Component/UpdateTable';

interface UpdateData {
  reasonForReview: string;
  changes: string;
  reasonForChanges: string;
  initiatedBy: string;
  approvedBy: string;
  reference: string;
  attachment: string[];
}

interface RiskAssessmentDetailProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  riskAssessmentId: string | null;
}

interface RiskAssessmentDetail {
  id: string;
  maskId: string;
  type: 'Routine' | 'Non Routine' | 'High-Risk Hazard';
  status: 'Draft' | 'Pending' | 'Published';
  created: string;
  updated: string;
  publishedDate?: string;
  nextReviewDate?: string;
  department?: {
    id: string;
    name: string;
  };
  workActivity?: {
    id: string;
    name: string;
  };
  nonRoutineWorkActivity?: string;
  nonRoutineDepartment?: string;
  hazardName?: string;
  description?: string;
  shortName?: string;
  teamLeader: {
    id: string;
    firstName: string;
    lastName?: string;
  };
  raTeamMembers: Array<{
    id: string;
    userId: string;
    signature?: string;
    signatureDate?: string;
    user: {
      id: string;
      firstName: string;
      lastName?: string;
      email?: string;
    };
  }>;
  tasks: Array<Array<{
    type: string;
    name?: string;
    images?: string[];
    selected?: any[];
    option?: any[];
    severity?: string | number;
    likelyhood?: string | number;
    level?: any;
    accept?: boolean;
    step?: number;
    value?: any;
  }>>;
  overallRecommendationOne?: {
    label: string;
    value: string;
  };
  overallRecommendationTwo?: {
    label: string;
    value: string;
  };
  additonalRemarks?: string;
  highRisk?: Array<{
    id: string;
    hazardName: string;
  }>;
  teamLeaderDeclaration?: {
    name?: string;
    sign?: string;
    signDate?: string;
  };
}

const RiskAssessmentDetailDialog: React.FC<RiskAssessmentDetailProps> = ({
  open,
  onOpenChange,
  riskAssessmentId,
}) => {
  const [riskAssessment, setRiskAssessment] = useState<RiskAssessmentDetail | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [subActivityDialogOpen, setSubActivityDialogOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<any[] | null>(null);
  const [selectedTaskIndex, setSelectedTaskIndex] = useState(0);
  const [updates, setUpdates] = useState<UpdateData[]>([]);

  const fetchRiskAssessmentDetails = useCallback(async () => {
    if (!riskAssessmentId) return;

    setLoading(true);
    setError(null);
    try {
      // API call structure based on test.js
      const uriString = {
        include: [
          { relation: "department" },
          { relation: "teamLeader" },
          { relation: "workActivity" },
          {
            relation: "raTeamMembers",
            scope: {
              include: [{ relation: "user" }]
            }
          }
        ]
      };

      const url = `/risk-assessments/${riskAssessmentId}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      const response = await apiService.get(url);

      // Transform the API response to match our interface
      const transformedData: RiskAssessmentDetail = {
        id: response.id,
        maskId: response.maskId || response.id,
        type: response.type,
        status: response.status,
        created: response.created,
        updated: response.updated,
        publishedDate: response.publishedDate,
        nextReviewDate: response.nextReviewDate,
        department: response.department,
        workActivity: response.workActivity,
        nonRoutineWorkActivity: response.nonRoutineWorkActivity,
        nonRoutineDepartment: response.nonRoutineDepartment,
        hazardName: response.hazardName,
        description: response.description,
        shortName: response.shortName,
        teamLeader: response.teamLeader,
        raTeamMembers: response.raTeamMembers || [],
        tasks: response.tasks || [],
        overallRecommendationOne: response.overallRecommendationOne,
        overallRecommendationTwo: response.overallRecommendationTwo,
        additonalRemarks: response.additonalRemarks,
        highRisk: response.highRisk || [],
        teamLeaderDeclaration: response.teamLeaderDeclaration
      };

      setRiskAssessment(transformedData);
    } catch (error) {
      console.error('Error fetching risk assessment details:', error);
      setError('Failed to load risk assessment details. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [riskAssessmentId]);

  const fetchRiskUpdates = useCallback(async () => {
    if (!riskAssessmentId) return;

    try {
      const response = await apiService.get(`/risk-assessments/${riskAssessmentId}/risk-updates`);
      setUpdates(response || []);
    } catch (error) {
      console.error('Error fetching risk updates:', error);
      // Don't show error toast for updates as it's not critical
      setUpdates([]);
    }
  }, [riskAssessmentId]);

  useEffect(() => {
    if (open && riskAssessmentId) {
      fetchRiskAssessmentDetails();
      fetchRiskUpdates();
    }
  }, [open, riskAssessmentId, fetchRiskAssessmentDetails, fetchRiskUpdates]);

  const handleViewTaskDetails = (task: any[], index: number) => {
    setSelectedTask(task);
    setSelectedTaskIndex(index);
    setSubActivityDialogOpen(true);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'Draft': { color: 'bg-gray-100 text-gray-800', icon: Clock },
      'Pending': { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      'Published': { color: 'bg-green-100 text-green-800', icon: CheckCircle }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;
    const Icon = config.icon;
    
    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        {status}
      </Badge>
    );
  };

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      'Routine': { color: 'bg-blue-100 text-blue-800' },
      'Non-Routine': { color: 'bg-orange-100 text-orange-800' },
      'High-Risk Hazard': { color: 'bg-red-100 text-red-800' }
    };
    
    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.Routine;
    
    return (
      <Badge className={config.color}>
        {type}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl">
          <div className="flex items-center justify-center py-12">
            <div className="text-center space-y-4">
              <Loader2 className="w-8 h-8 animate-spin mx-auto text-blue-500" />
              <p className="text-gray-600">Loading risk assessment details...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (error) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl">
          <div className="flex items-center justify-center py-12">
            <div className="text-center space-y-4">
              <AlertTriangle className="w-12 h-12 mx-auto text-red-500" />
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">Error Loading Details</h3>
                <p className="text-gray-600 mb-4">{error}</p>
                <Button onClick={fetchRiskAssessmentDetails} variant="outline">
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (!riskAssessment) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-hidden bg-white">
        <DialogHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200 p-6 -m-6 mb-0">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                <FileText className="w-6 h-6 text-white" />
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-3">
                  <DialogTitle className="text-2xl font-bold text-gray-800">
                    {riskAssessment.type === 'Routine' ?
                      `${riskAssessment.department?.name} - ${riskAssessment.workActivity?.name}` :
                      riskAssessment.type === 'Non Routine' ?
                      riskAssessment.nonRoutineWorkActivity || 'Non-Routine Risk Assessment' :
                      riskAssessment.hazardName || 'High-Risk Hazard Assessment'
                    }
                  </DialogTitle>
                  {getTypeBadge(riskAssessment.type)}
                  {getStatusBadge(riskAssessment.status)}
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    <span>Created by {riskAssessment.teamLeader.firstName} {riskAssessment.teamLeader.lastName}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    <span>Created: {formatDate(riskAssessment.created)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    <span>Updated: {formatDate(riskAssessment.updated)}</span>
                  </div>
                </div>
                <p className="text-gray-600 text-sm">
                  ID: {riskAssessment.maskId}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <GenerateLandPdf pdf={riskAssessment} />
            </div>
          </div>
        </DialogHeader>

        <div className="overflow-y-auto max-h-[calc(95vh-120px)] p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className={`grid w-full mb-6 ${riskAssessment.type === 'High-Risk Hazard' ? 'grid-cols-4' : 'grid-cols-5'}`}>
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <FileText className="w-4 h-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="assessment" className="flex items-center gap-2">
                <AlertTriangle className="w-4 h-4" />
                Risk Assessment
              </TabsTrigger>
              {riskAssessment.type !== 'High-Risk Hazard' && (
                <TabsTrigger value="controls" className="flex items-center gap-2">
                  <Shield className="w-4 h-4" />
                  Recommendation
                </TabsTrigger>
              )}
              <TabsTrigger value="team" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Team & Signatures
              </TabsTrigger>
              <TabsTrigger value="changelog" className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Change Log
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Activity Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="w-5 h-5 text-blue-600" />
                    Activity Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {riskAssessment.type === 'Routine' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Building className="w-4 h-4 text-gray-500" />
                          <h6 className="font-semibold text-gray-700">Operational Risk Area</h6>
                        </div>
                        <p className="text-gray-600 pl-6">{riskAssessment.department?.name}</p>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Activity className="w-4 h-4 text-gray-500" />
                          <h6 className="font-semibold text-gray-700">Work Activity</h6>
                        </div>
                        <p className="text-gray-600 pl-6">{riskAssessment.workActivity?.name}</p>
                      </div>
                    </div>
                  )}
                  
                  {riskAssessment.type === 'Non Routine' && (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Activity className="w-4 h-4 text-gray-500" />
                        <h6 className="font-semibold text-gray-700">Work Activity</h6>
                      </div>
                      <p className="text-gray-600 pl-6">{riskAssessment.nonRoutineWorkActivity}</p>
                    </div>
                  )}
                  
                  {riskAssessment.type === 'High-Risk Hazard' && (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="w-4 h-4 text-red-500" />
                        <h6 className="font-semibold text-gray-700">Hazard Name</h6>
                      </div>
                      <p className="text-gray-600 pl-6">{riskAssessment.hazardName}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Description */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5 text-green-600" />
                    Description
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 leading-relaxed">{riskAssessment.description}</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="assessment" className="space-y-6">
              {riskAssessment.type === 'High-Risk Hazard' ? (
                // High-Risk Hazard Assessment Display
                <div className="space-y-6">
                  {/* Consequences Section */}
                  <Card className="border-orange-200 bg-orange-50/30">
                    <CardHeader className="bg-gradient-to-r from-orange-100 to-red-100 border-b border-orange-200">
                      <CardTitle className="flex items-center gap-3 text-lg">
                        <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                          <AlertTriangle className="w-4 h-4 text-white" />
                        </div>
                        Consequences
                      </CardTitle>
                      <p className="text-gray-700 mt-2 leading-relaxed">
                        Potential consequences of this Critical High Risk Activity on Personnel, Environment,
                        Equipment / Property & Service due to the associated high-risk factors.
                      </p>
                    </CardHeader>
                    <CardContent className="p-6">
                      {riskAssessment.tasks[0]?.[0]?.option?.length > 0 ? (
                        <div className="space-y-4">
                          {riskAssessment.tasks[0][0].option.map((consequence: any, idx: number) => (
                            <div key={idx} className="border rounded-lg p-4 bg-white">
                              <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                  <span className="text-orange-600 font-bold text-sm">{idx + 1}</span>
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-2">
                                    <Badge className="bg-orange-100 text-orange-800">
                                      {consequence.current_type || 'Impact Area'}
                                    </Badge>
                                    {consequence.files && consequence.files.length > 0 && (
                                      <Badge className="bg-blue-100 text-blue-800 flex items-center gap-1">
                                        <Paperclip className="w-3 h-3" />
                                        {consequence.files.length} file{consequence.files.length > 1 ? 's' : ''}
                                      </Badge>
                                    )}
                                  </div>
                                  <p className="text-gray-700 mb-3">{consequence.value || 'No description provided'}</p>

                                  {/* Display attached files */}
                                  {consequence.files && consequence.files.length > 0 && (
                                    <div className="space-y-2">
                                      <h6 className="text-sm font-medium text-gray-600">Attached Files:</h6>
                                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                        {consequence.files.map((fileName: string, fileIdx: number) => (
                                          <div key={fileIdx} className="relative group">
                                            <div className="border rounded-lg overflow-hidden bg-gray-50 hover:bg-gray-100 transition-colors">
                                              <div className="w-full h-24 flex items-center justify-center">
                                                <ImageComponent
                                                  fileName={fileName}
                                                  size="150"
                                                  name={false}
                                                />
                                              </div>
                                              <div className="p-2">
                                                <p className="text-xs text-gray-600 truncate" title={fileName}>
                                                  {fileName}
                                                </p>
                                              </div>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <AlertTriangle className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                          <p>No consequences identified</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Necessary Controls Section */}
                  <Card className="border-blue-200 bg-blue-50/30">
                    <CardHeader className="bg-gradient-to-r from-blue-100 to-indigo-100 border-b border-blue-200">
                      <CardTitle className="flex items-center gap-3 text-lg">
                        <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                          <Shield className="w-4 h-4 text-white" />
                        </div>
                        Necessary Controls
                      </CardTitle>
                      <p className="text-gray-700 mt-2 leading-relaxed">
                        Controls to manage the hazards, associated risks, and potential consequences of this Critical High Risk Activity.
                        These controls will also reflect in the Permit to Work Applications when the work involves these High Risk Activities.
                      </p>
                    </CardHeader>
                    <CardContent className="p-6">
                      {riskAssessment.tasks[0]?.[1]?.option?.length > 0 ? (
                        <div className="space-y-4">
                          {riskAssessment.tasks[0][1].option.map((control: any, idx: number) => (
                            <div key={idx} className="border rounded-lg p-4 bg-white">
                              <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                  <span className="text-blue-600 font-bold text-sm">{idx + 1}</span>
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-2">
                                    <Badge className="bg-blue-100 text-blue-800">
                                      {control.current_type || 'Control Type'}
                                    </Badge>
                                    {control.method && (
                                      <Badge className="bg-gray-100 text-gray-800">
                                        {control.method}
                                      </Badge>
                                    )}
                                    {control.required && (
                                      <Badge className="bg-red-100 text-red-800">
                                        Required
                                      </Badge>
                                    )}
                                    {control.validity && (
                                      <Badge className="bg-green-100 text-green-800">
                                        Valid
                                      </Badge>
                                    )}
                                    {control.files && control.files.length > 0 && (
                                      <Badge className="bg-purple-100 text-purple-800 flex items-center gap-1">
                                        <Paperclip className="w-3 h-3" />
                                        {control.files.length} file{control.files.length > 1 ? 's' : ''}
                                      </Badge>
                                    )}
                                  </div>
                                  <p className="text-gray-700 mb-3">{control.value || 'No description provided'}</p>

                                  {/* Display attached files */}
                                  {control.files && control.files.length > 0 && (
                                    <div className="space-y-2">
                                      <h6 className="text-sm font-medium text-gray-600">Attached Files:</h6>
                                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                        {control.files.map((fileName: string, fileIdx: number) => (
                                          <div key={fileIdx} className="relative group">
                                            <div className="border rounded-lg overflow-hidden bg-gray-50 hover:bg-gray-100 transition-colors">
                                              <div className="w-full h-24 flex items-center justify-center">
                                                <ImageComponent
                                                  fileName={fileName}
                                                  size="150"
                                                  name={false}
                                                />
                                              </div>
                                              <div className="p-2">
                                                <p className="text-xs text-gray-600 truncate" title={fileName}>
                                                  {fileName}
                                                </p>
                                              </div>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <Shield className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                          <p>No controls identified</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              ) : (
                // Routine/Non-Routine Assessment Display
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <AlertTriangle className="w-5 h-5 text-orange-600" />
                      Sub-Activities Risk Assessment
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {riskAssessment.tasks.length === 0 ? (
                      <div className="text-center py-12 text-gray-500">
                        <AlertTriangle className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                        <h3 className="text-lg font-medium text-gray-600 mb-2">No Sub-Activities Assessed</h3>
                        <p className="text-gray-500">This risk assessment doesn't have any sub-activities defined yet.</p>
                      </div>
                    ) : (
                      <div className="space-y-6">
                        {riskAssessment.tasks.map((task, index) => (
                          <div key={index} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                            <div className="flex items-start justify-between mb-4">
                              <div className="flex items-start gap-4">
                                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                                  <span className="text-blue-600 font-semibold text-sm">{index + 1}</span>
                                </div>
                                <div>
                                  <h4 className="text-lg font-semibold text-gray-800 mb-2">{task[0]?.name || `Sub-Activity ${index + 1}`}</h4>
                                  <div className="flex items-center gap-4">
                                    <Badge className="bg-blue-100 text-blue-800">
                                      <Activity className="w-3 h-3 mr-1" />
                                      {task[0]?.type || 'Sub-Activity'}
                                    </Badge>
                                    {task[4]?.level && (
                                      <Badge
                                        className={
                                          task[4].level === 'High' ? 'bg-red-100 text-red-800' :
                                          task[4].level === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                          'bg-green-100 text-green-800'
                                        }
                                      >
                                        <AlertTriangle className="w-3 h-3 mr-1" />
                                        {task[4].level} Risk
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleViewTaskDetails(task, index)}
                              >
                                <FileText className="w-4 h-4 mr-2" />
                                View Details
                              </Button>
                            </div>

                            <div className="ml-12 space-y-3">
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div className="bg-gray-50 p-3 rounded">
                                  <h6 className="font-medium text-gray-700 mb-1">Hazards Identified</h6>
                                  <p className="text-gray-600">{task[1]?.selected?.length || 0} hazards assessed</p>
                                </div>
                                <div className="bg-gray-50 p-3 rounded">
                                  <h6 className="font-medium text-gray-700 mb-1">Controls in Place</h6>
                                  <p className="text-gray-600">{task[3]?.option?.length || 0} control measures</p>
                                </div>
                                <div className="bg-gray-50 p-3 rounded">
                                  <h6 className="font-medium text-gray-700 mb-1">Risk Rating</h6>
                                  <p className="text-gray-600">
                                    {(() => {
                                      const severity = Number(task[4]?.severity) || 0;
                                      const likelihood = Number(task[4]?.likelyhood) || 0;
                                      const riskValue = severity * likelihood;
                                      if (riskValue >= 15) return `${riskValue} (High)`;
                                      if (riskValue >= 8) return `${riskValue} (Medium)`;
                                      if (riskValue > 0) return `${riskValue} (Low)`;
                                      return 'Not Assessed';
                                    })()}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}

                        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                          <div className="flex items-start gap-3">
                            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                              <span className="text-white text-xs">ℹ</span>
                            </div>
                            <div>
                              <h6 className="font-medium text-blue-800 mb-1">Assessment Summary</h6>
                              <p className="text-blue-700 text-sm">
                                This risk assessment includes {riskAssessment.tasks.length} sub-activities with varying risk levels.
                                Each sub-activity has been evaluated for hazards, consequences, current controls, and additional control measures.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Only show Controls tab for Routine and Non-Routine assessments */}
            {riskAssessment.type !== 'High-Risk Hazard' && (
              <TabsContent value="controls" className="space-y-6">
                {/* Recommendations */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="w-5 h-5 text-blue-600" />
                      Overall Recommendations
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <h6 className="font-semibold text-gray-700">Recommendation One</h6>
                        <p className="text-gray-600">{riskAssessment.overallRecommendationOne?.label || 'No recommendation provided'}</p>
                      </div>
                      <div className="space-y-2">
                        <h6 className="font-semibold text-gray-700">Recommendation Two</h6>
                        <p className="text-gray-600">{riskAssessment.overallRecommendationTwo?.label || 'No recommendation provided'}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* High-Risk Permits */}
                {riskAssessment.highRisk && riskAssessment.highRisk.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <AlertTriangle className="w-5 h-5 text-red-600" />
                        Required High-Risk Permits
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {riskAssessment.highRisk.map((permit) => (
                          <Badge key={permit.id} variant="outline" className="p-2 justify-start">
                            <AlertTriangle className="w-4 h-4 mr-2 text-red-500" />
                            {permit.hazardName}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Additional Remarks */}
                {riskAssessment.additonalRemarks && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Additional Recommendations</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700 leading-relaxed">{riskAssessment.additonalRemarks}</p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
            )}

            <TabsContent value="team" className="space-y-6">
              {/* Team Members */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5 text-purple-600" />
                    Team Members
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {riskAssessment.raTeamMembers.map((member) => (
                      <div key={member.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                            <User className="w-5 h-5 text-purple-600" />
                          </div>
                          <div>
                            <p className="font-medium">{member.user.firstName} {member.user.lastName}</p>
                            <p className="text-sm text-gray-500">{member.user.email}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          {member.signature ? (
                            <div className="flex items-center gap-2">
                              <CheckCircle className="w-5 h-5 text-green-500" />
                              <div>
                                <p className="text-sm font-medium text-green-600">Signed</p>
                                <p className="text-xs text-gray-500">{formatDate(member.signatureDate!)}</p>
                              </div>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <Clock className="w-5 h-5 text-yellow-500" />
                              <p className="text-sm text-yellow-600">Pending</p>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Team Leader Declaration */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="w-5 h-5 text-blue-600" />
                    Team Leader Declaration
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-800 leading-relaxed">
                        {riskAssessment.type === 'Routine'
                          ? "As the Team Leader for this Routine Risk Assessment, I affirm my role in guiding the identification of potential risks and necessary controls. The controls listed have been evaluated by the team based on their professional expertise and are essential for ensuring safety in this activity."
                          : riskAssessment.type === 'Non Routine'
                          ? "As the Team Leader for this Non-Routine Risk Assessment, I confirm my responsibility in guiding the team through the identification of potential risks and necessary controls for activities that are not part of the organization's routine work activity inventory."
                          : "As the Team Leader for this exercise, I confirm my role in identifying the potential consequences of this Critical High Risk Activity and in outlining the necessary controls."
                        }
                      </p>
                    </div>
                    
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <User className="w-6 h-6 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-semibold">{riskAssessment.teamLeader.firstName} {riskAssessment.teamLeader.lastName}</p>
                          <p className="text-sm text-gray-500">Team Leader</p>
                        </div>
                      </div>
                      <div className="text-right">
                        {riskAssessment.teamLeaderDeclaration?.sign ? (
                          <div className="flex items-center gap-2">
                            <CheckCircle className="w-6 h-6 text-green-500" />
                            <div>
                              <p className="font-medium text-green-600">Signed & Approved</p>
                              <p className="text-sm text-gray-500">{formatDate(riskAssessment.teamLeaderDeclaration.signDate!)}</p>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <Clock className="w-6 h-6 text-yellow-500" />
                            <p className="text-yellow-600">Pending Signature</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="changelog" className="space-y-6">
              <Card className="border-gray-200 bg-gray-50/30">
                <CardHeader className="bg-gradient-to-r from-gray-600 to-gray-700 text-white">
                  <CardTitle className="flex items-center gap-3 text-lg">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <Clock className="w-4 h-4 text-white" />
                    </div>
                    Risk Assessment Change Log
                  </CardTitle>
                  <p className="text-gray-100 mt-2 leading-relaxed">
                    Track all updates and modifications made to this risk assessment over time.
                    This includes reviews, changes, and the reasons behind each modification.
                  </p>
                </CardHeader>
                <CardContent className="p-6">
                  {updates.length > 0 ? (
                    <UpdateTable data={updates} />
                  ) : (
                    <div className="text-center py-12">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Clock className="w-8 h-8 text-gray-400" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Changes Recorded</h3>
                      <p className="text-gray-500 max-w-md mx-auto">
                        No updates have been made to this risk assessment yet.
                        Any future modifications will be tracked and displayed here.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>

      {/* Sub-Activity Detail Dialog */}
      <SubActivityDetailDialog
        open={subActivityDialogOpen}
        onOpenChange={setSubActivityDialogOpen}
        task={selectedTask}
        taskIndex={selectedTaskIndex}
      />
    </Dialog>
  );
};

export default RiskAssessmentDetailDialog;
