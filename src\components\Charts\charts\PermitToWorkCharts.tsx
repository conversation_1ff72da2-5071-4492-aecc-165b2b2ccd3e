import { useState, useMemo } from "react";
import MetricCard from "@/components/Charts/MetricCard";
import MetricDetails from "@/components/Charts/MetricDetails";
import { LocationFilterState } from "@/types/locationFilter";


interface PermitToWorkChartsProps {
  locationFilters?: LocationFilterState;
}

const PermitToWorkCharts = ({ locationFilters }: PermitToWorkChartsProps) => {
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});


  // Permit to Work metrics data
  const overviewMetrics = [
    {
      id: "permits-approved-on-time",
      title: "% of Permits Approved On-Time",
      value: "94",
      unit: "permits (94%)",
      target: 100,
      targetPercentage: 6,
      trend: [90, 91, 92, 93, 94, 95, 94, 93, 94, 95, 96, 94],
      isImproving: true
    },
    {
      id: "permits-closed-properly",
      title: "% of Permits Closed Properly",
      value: "89",
      unit: "permits (89%)",
      target: 100,
      targetPercentage: 11,
      trend: [85, 86, 87, 88, 89, 90, 89, 88, 89, 90, 91, 89],
      isImproving: true
    },
    {
      id: "permits-overdue-closure",
      title: "% of Permits Overdue for Closure",
      value: "3",
      unit: "permits (3%)",
      target: 0,
      targetPercentage: 3,
      trend: [6, 5, 4, 3, 2, 3, 4, 3, 2, 3, 4, 3],
      isImproving: false
    },
    {
      id: "permit-violations",
      title: "Number of Permit Violations",
      value: "2",
      unit: "violations",
      target: 0,
      targetPercentage: 100,
      trend: [5, 4, 3, 2, 1, 2, 3, 2, 1, 2, 3, 2],
      isImproving: false
    }
  ];

  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  return (
    <div className="space-y-6">

      {/* Headline Metrics */}
      <div className="grid grid-cols-4 gap-4">
        {overviewMetrics.map((metric) => (
          <MetricCard
            key={metric.id}
            {...metric}
            showDetails={openDetailsId === metric.id}
            onToggleDetails={() => handleToggleDetails(metric.id)}
            selectedPeriod={getSelectedPeriod(metric.id)}
            onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
          />
        ))}
      </div>

      {/* Details Section - Rendered below the grid when a card is selected */}
      {openDetailsId && (() => {
        const selectedMetric = overviewMetrics.find(m => m.id === openDetailsId);
        return selectedMetric ? (
          <MetricDetails
            title={selectedMetric.title}
            value={selectedMetric.value}
            unit={selectedMetric.unit}
            targetPercentage={selectedMetric.targetPercentage}
            trend={selectedMetric.trend}
            isImproving={selectedMetric.isImproving}
            selectedPeriod={getSelectedPeriod(openDetailsId)}
          />
        ) : null;
      })()}
    </div>
  );
};

export default PermitToWorkCharts;
